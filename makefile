# 环境变量
OUTPUT_PATH:=./build
GO_MAIN_PATH:=cmd/main.go
BUILD_FLAG:=--ldflags="-checklinkname=0"

# 编译当前平台适用的二进制包
build: build-frontend
	CGO_ENABLED=0 go build $(BUILD_FLAG) -o $(OUTPUT_PATH)/fn_helper $(GO_MAIN_PATH)

# 编译各个架构的二进制包
build-all: build-frontend
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build $(BUILD_FLAG) -o $(OUTPUT_PATH)/fn_helper_linux_amd64 $(GO_MAIN_PATH)
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build $(BUILD_FLAG) -o $(OUTPUT_PATH)/fn_helper_linux_arm64 $(GO_MAIN_PATH)
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 go build $(BUILD_FLAG) -o $(OUTPUT_PATH)/fn_helper_darwin_amd64 $(GO_MAIN_PATH)
	CGO_ENABLED=0 GOOS=darwin GOARCH=arm64 go build $(BUILD_FLAG) -o $(OUTPUT_PATH)/fn_helper_darwin_arm64 $(GO_MAIN_PATH)
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 go build $(BUILD_FLAG) -o $(OUTPUT_PATH)/fn_helper_windows_amd64.exe $(GO_MAIN_PATH)
	CGO_ENABLED=0 GOOS=windows GOARCH=arm64 go build $(BUILD_FLAG) -o $(OUTPUT_PATH)/fn_helper_windows_arm64.exe $(GO_MAIN_PATH)

# 构建前端
build-frontend: clean-frontend pnpm-install
	cd frontend && npm run build && rm -rf ../cmd/dist && mv -f dist ../cmd/

clean-frontend:
	rm -rf frontend/dist

# 安装 pnpm 依赖
pnpm-install:
	cd frontend && pnpm install --registry=https://registry.npmmirror.com

clean:
	rm -rf $(OUTPUT_PATH)

run: build
	$(OUTPUT_PATH)/fn_helper

run-debug: build
	$(OUTPUT_PATH)/fn_helper -debug

.PHONY: build build-frontend pnpm-install clean
