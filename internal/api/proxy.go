package api

import (
	"fmt"
	"net/http"

	"fn_helper/internal/feature/config"
	"fn_helper/internal/feature/proxy"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/context"
)

type ProxyAPI struct {
	Manager *proxy.ProxyManager
}

func RegisterProxyAPI(party iris.Party, manager *proxy.ProxyManager) {
	api := &ProxyAPI{
		Manager: manager,
	}

	proxyRoutes := party.Party("/proxy")
	{
		proxyRoutes.Get("/", api.handleQueryAllProxies)
		proxyRoutes.Post("/", api.handleNewProxy)
		proxyRoutes.Get("/{id}", api.handleGetProxy)
		proxyRoutes.Put("/{id}", api.handleUpdateProxy)
		proxyRoutes.Post("/{id}/start", api.handleStartProxy)
		proxyRoutes.Post("/{id}/stop", api.handleStopProxy)
		proxyRoutes.Delete("/{id}", api.handleDeleteProxy)

		// Proxy Location APIs
		proxyRoutes.Post("/{proxyID}/locations", api.handleNewProxyLocation)
		proxyRoutes.Get("/{proxyID}/locations/{path}", api.handleGetProxyLocation)
		proxyRoutes.Put("/{proxyID}/locations/{path}", api.handleUpdateProxyLocation)
		proxyRoutes.Post("/{proxyID}/locations/{path}/start", api.handleStartProxyLocation)
		proxyRoutes.Post("/{proxyID}/locations/{path}/stop", api.handleStopProxyLocation)
		proxyRoutes.Delete("/{proxyID}/locations/{path}", api.handleDeleteProxyLocation)
	}
}

func (api *ProxyAPI) handleQueryAllProxies(ctx *context.Context) {
	allProxies := api.Manager.QueryAll()
	rules := make([]config.ProxyRule, 0, len(allProxies))
	for _, p := range allProxies {
		rule := p.GetRule()
		// 设置运行时状态
		rule.Running = p.IsRunning()
		rules = append(rules, rule)
	}
	ctx.JSON(rules)
}

func (api *ProxyAPI) handleNewProxy(ctx *context.Context) {
	var rule config.ProxyRule
	if err := ctx.ReadJSON(&rule); err != nil {
		ctx.StopWithError(http.StatusBadRequest, fmt.Errorf("无效的请求体: %v", err))
		return
	}

	newProxy := proxy.NewWebProxy(rule)
	if err := api.Manager.NewProxy(newProxy); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("创建代理失败: %v", err))
		return
	}

	ctx.StatusCode(http.StatusOK)
	// 返回包含已生成的 ID 的完整规则
	ctx.JSON(newProxy.GetRule())
}

func (api *ProxyAPI) handleGetProxy(ctx *context.Context) {
	id := ctx.Params().GetString("id")
	prx, err := api.Manager.GetProxy(id)
	if err != nil {
		ctx.StopWithError(http.StatusNotFound, fmt.Errorf("代理未找到: %v", err))
		return
	}
	ctx.JSON(prx.GetRule())
}

func (api *ProxyAPI) handleUpdateProxy(ctx *context.Context) {
	id := ctx.Params().GetString("id")
	var rule config.ProxyRule
	if err := ctx.ReadJSON(&rule); err != nil {
		ctx.StopWithError(http.StatusBadRequest, fmt.Errorf("无效的请求体: %v", err))
		return
	}

	// 确保规则ID与路径ID一致
	if rule.Id != id {
		ctx.StopWithError(http.StatusBadRequest, fmt.Errorf("请求体中的代理ID与路径ID不匹配"))
		return
	}

	// 创建一个临时的WebProxy实例，只用于传递rule
	updatedProxy := proxy.NewWebProxy(rule)
	if err := api.Manager.UpdateProxy(updatedProxy); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("更新代理失败: %v", err))
		return
	}

	ctx.StatusCode(http.StatusOK)
	ctx.JSON(rule)
}

func (api *ProxyAPI) handleStartProxy(ctx *context.Context) {
	id := ctx.Params().GetString("id")
	if err := api.Manager.StartProxy(id); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("启动代理失败: %v", err))
		return
	}
	ctx.StatusCode(http.StatusOK)
	ctx.WriteString(fmt.Sprintf("代理 %s 已启动", id))
}

func (api *ProxyAPI) handleStopProxy(ctx *context.Context) {
	id := ctx.Params().GetString("id")
	if err := api.Manager.StopProxy(id); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("停止代理失败: %v", err))
		return
	}
	ctx.StatusCode(http.StatusOK)
	ctx.WriteString(fmt.Sprintf("代理 %s 已停止", id))
}

func (api *ProxyAPI) handleDeleteProxy(ctx *context.Context) {
	id := ctx.Params().GetString("id")
	if err := api.Manager.DeleteProxy(id); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("删除代理失败: %v", err))
		return
	}
	ctx.StatusCode(http.StatusOK)
	ctx.WriteString(fmt.Sprintf("代理 %s 已删除", id))
}

func (api *ProxyAPI) handleNewProxyLocation(ctx *context.Context) {
	proxyID := ctx.Params().GetString("proxyID")
	var loc config.ProxyLocation
	if err := ctx.ReadJSON(&loc); err != nil {
		ctx.StopWithError(http.StatusBadRequest, fmt.Errorf("无效的请求体: %v", err))
		return
	}

	if err := api.Manager.NewProxyLocation(proxyID, loc); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("添加代理Location失败: %v", err))
		return
	}
	ctx.StatusCode(http.StatusOK)
	ctx.JSON(loc)
}

func (api *ProxyAPI) handleGetProxyLocation(ctx *context.Context) {
	proxyID := ctx.Params().GetString("proxyID")
	path := ctx.Params().GetString("path")

	loc, err := api.Manager.GetProxyLocation(proxyID, path)
	if err != nil {
		ctx.StopWithError(http.StatusNotFound, fmt.Errorf("代理Location未找到: %v", err))
		return
	}
	ctx.JSON(loc)
}

func (api *ProxyAPI) handleUpdateProxyLocation(ctx *context.Context) {
	proxyID := ctx.Params().GetString("proxyID")
	path := ctx.Params().GetString("path")
	var loc config.ProxyLocation
	if err := ctx.ReadJSON(&loc); err != nil {
		ctx.StopWithError(http.StatusBadRequest, fmt.Errorf("无效的请求体: %v", err))
		return
	}

	// 确保请求体中的path与路径中的path一致
	if loc.Path != path {
		ctx.StopWithError(http.StatusBadRequest, fmt.Errorf("请求体中的Location路径与路径ID不匹配"))
		return
	}

	if err := api.Manager.UpdateProxyLocation(proxyID, loc); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("更新代理Location失败: %v", err))
		return
	}
	ctx.StatusCode(http.StatusOK)
	ctx.JSON(loc)
}

func (api *ProxyAPI) handleStartProxyLocation(ctx *context.Context) {
	proxyID := ctx.Params().GetString("proxyID")
	path := ctx.Params().GetString("path")

	// 为了调用Manager的StartProxyLocation，需要构建一个ProxyLocation对象
	// 假设我们只需要path来识别，其他字段可以为空或默认值
	loc := config.ProxyLocation{Path: path}

	if err := api.Manager.StartProxyLocation(proxyID, loc); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("启动代理Location失败: %v", err))
		return
	}
	ctx.StatusCode(http.StatusOK)
	ctx.WriteString(fmt.Sprintf("代理 %s 的Location %s 已启动", proxyID, path))
}

func (api *ProxyAPI) handleStopProxyLocation(ctx *context.Context) {
	proxyID := ctx.Params().GetString("proxyID")
	path := ctx.Params().GetString("path")

	// 为了调用Manager的StopProxyLocation，需要构建一个ProxyLocation对象
	loc := config.ProxyLocation{Path: path}

	if err := api.Manager.StopProxyLocation(proxyID, loc); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("停止代理Location失败: %v", err))
		return
	}
	ctx.StatusCode(http.StatusOK)
	ctx.WriteString(fmt.Sprintf("代理 %s 的Location %s 已停止", proxyID, path))
}

func (api *ProxyAPI) handleDeleteProxyLocation(ctx *context.Context) {
	proxyID := ctx.Params().GetString("proxyID")
	path := ctx.Params().GetString("path")

	// 为了调用Manager的DeleteProxyLocation，需要构建一个ProxyLocation对象
	loc := config.ProxyLocation{Path: path}

	if err := api.Manager.DeleteProxyLocation(proxyID, loc); err != nil {
		ctx.StopWithError(http.StatusInternalServerError, fmt.Errorf("删除代理Location失败: %v", err))
		return
	}
	ctx.StatusCode(http.StatusOK)
	ctx.WriteString(fmt.Sprintf("代理 %s 的Location %s 已删除", proxyID, path))
}
