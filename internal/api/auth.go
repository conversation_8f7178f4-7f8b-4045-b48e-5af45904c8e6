package api

import (
	"github.com/alexedwards/argon2id"
	"net/http"

	"fn_helper/internal/feature/config"
	"fn_helper/internal/session"
	"github.com/kataras/iris/v12"
	"go.uber.org/zap"
)

type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// <PERSON><PERSON> handles administrator login.
func Login(ctx iris.Context) {
	var req LoginRequest
	if err := ctx.ReadJSON(&req); err != nil {
		zap.L().Error("Failed to read login request", zap.Error(err))
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.JSON(iris.Map{"message": "Invalid request body"})
		return
	}

	cfg := config.Global()

	// Verify username and password
	if req.Username != cfg.AdminUser.Username {
		zap.L().Warn("Login failed: Invalid username", zap.String("username", req.Username))
		ctx.StatusCode(iris.StatusUnauthorized)
		ctx.JSON(iris.Map{"message": "Invalid username or password"})
		return
	}

	// Verify password using Argon2
	matches, err := argon2id.ComparePasswordAndHash(req.Password, cfg.AdminUser.Password)
	if err != nil || !matches {
		zap.L().Warn("Login failed: Invalid password", zap.String("username", req.Username), zap.Error(err))
		ctx.StatusCode(iris.StatusUnauthorized)
		ctx.JSON(iris.Map{"message": "Invalid username or password"})
		return
	}

	// Create a new session and get a secure token
	token, err := session.Global().New(req.Username)
	if err != nil {
		zap.L().Error("Failed to create session", zap.Error(err))
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.JSON(iris.Map{"message": "Failed to create session"})
		return
	}

	// Set session cookie
	ctx.SetCookie(buildCookie(token))

	zap.L().Info("Admin user logged in successfully", zap.String("username", req.Username))
	ctx.StatusCode(iris.StatusOK)
	ctx.JSON(iris.Map{"message": "Login successful"})
}

// Refresh handles refreshing the session cookie.
func Refresh(ctx iris.Context) {
	// Get the old token from the cookie
	oldToken := ctx.GetCookie("session_id")
	if oldToken == "" {
		ctx.StatusCode(iris.StatusUnauthorized)
		ctx.JSON(iris.Map{"message": "Unauthorized"})
		return
	}

	// Refresh the session and get a new token
	newToken, err := session.Global().Refresh(oldToken)
	if err != nil {
		zap.L().Warn("Failed to refresh session", zap.Error(err))
		ctx.StatusCode(iris.StatusUnauthorized)
		ctx.JSON(iris.Map{"message": "Invalid or expired session"})
		return
	}

	// Set the new session cookie
	ctx.SetCookie(buildCookie(newToken))

	ctx.StatusCode(iris.StatusOK)
	ctx.JSON(iris.Map{"message": "Session refreshed successfully"})
}

func buildCookie(token string) *iris.Cookie {
	return &iris.Cookie{
		Name:     "session_id",
		Value:    token,
		HttpOnly: true,
		Path:     "/",
		SameSite: http.SameSiteLaxMode,
		MaxAge:   int(session.SessionDuration.Seconds()),
	}
}

// Logout handles administrator logout.
func Logout(ctx iris.Context) {
	// Get the session token from the cookie
	token := ctx.GetCookie("session_id")
	if token != "" {
		// Delete the session from the store. Assuming session.Global() has a Delete method.
		session.Global().Delete(token)
	}

	// Expire the cookie on the client's browser by setting a past expiry date
	ctx.SetCookie(&iris.Cookie{
		Name:     "session_id",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		MaxAge:   -1, // This tells the browser to delete the cookie
		SameSite: http.SameSiteLaxMode,
	})

	zap.L().Info("User logged out successfully")
	ctx.StatusCode(iris.StatusOK)
	ctx.JSON(iris.Map{"message": "Logout successful"})
}
