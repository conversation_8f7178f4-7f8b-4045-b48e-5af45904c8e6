package session

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

const (
	SessionDuration = 30 * time.Minute
	sessionLength   = 32 // 32 bytes for a 64-character hex string
)

// Session holds the data for a single session.
type Session struct {
	Username string
	Expires  time.Time
}

// IsExpired returns true if the session has expired.
func (s Session) IsExpired() bool {
	return s.Expires.Before(time.Now())
}

// Store manages all active sessions.
type Store struct {
	sessions map[string]Session
	mu       sync.RWMutex
	stopChan chan struct{}
	wg       sync.WaitGroup
}

var (
	globalStore *Store
	once        sync.Once
)

// Global returns the singleton session store instance.
func Global() *Store {
	once.Do(func() {
		globalStore = &Store{
			sessions: make(map[string]Session),
			stopChan: make(chan struct{}),
		}
		globalStore.StartCleanup()
	})
	return globalStore
}

// StartCleanup starts a goroutine that periodically cleans up expired sessions.
func (s *Store) StartCleanup() {
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		ticker := time.NewTicker(5 * time.Minute) // Clean up every 5 minutes
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				s.cleanupExpiredSessions()
			case <-s.stopChan:
				zap.L().Info("Session cleanup goroutine stopped.")
				return
			}
		}
	}()
}

// StopCleanup stops the session cleanup goroutine.
func (s *Store) StopCleanup() {
	close(s.stopChan)
	s.wg.Wait()
}

// cleanupExpiredSessions iterates through sessions and removes expired ones.
func (s *Store) cleanupExpiredSessions() {
	s.mu.Lock()
	defer s.mu.Unlock()

	count := 0
	for token, sess := range s.sessions {
		if sess.IsExpired() {
			delete(s.sessions, token)
			count++
		}
	}
	zap.L().Info("Cleaned up expired sessions", zap.Int("count", count))
}

// New creates a new session for the given username, generates a secure token,
// and stores it. It returns the token and any error that occurred.
func (s *Store) New(username string) (string, error) {
	token, err := generateSecureToken(sessionLength)
	if err != nil {
		return "", fmt.Errorf("failed to generate session token: %w", err)
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	s.sessions[token] = Session{
		Username: username,
		Expires:  time.Now().Add(SessionDuration),
	}

	return token, nil
}

// Get retrieves a session from the store. It returns the session and
// a boolean indicating whether the session was found and is not expired.
func (s *Store) Get(token string) (Session, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	session, found := s.sessions[token]
	if !found || session.IsExpired() {
		return Session{}, false
	}

	return session, true
}

// Delete removes a session from the store.
func (s *Store) Delete(token string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	delete(s.sessions, token)
}

// Refresh updates the expiration time of an existing session and returns
// a new token for it. If the old token is invalid, it returns an error.
func (s *Store) Refresh(oldToken string) (string, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	session, found := s.sessions[oldToken]
	if !found || session.IsExpired() {
		return "", fmt.Errorf("invalid or expired session token")
	}

	// Delete the old session
	delete(s.sessions, oldToken)

	// Create a new token
	newToken, err := generateSecureToken(sessionLength)
	if err != nil {
		return "", fmt.Errorf("failed to generate new session token: %w", err)
	}

	// Store the session with the new token and updated expiration
	s.sessions[newToken] = Session{
		Username: session.Username,
		Expires:  time.Now().Add(SessionDuration),
	}

	return newToken, nil
}

// generateSecureToken creates a random, securely generated token.
func generateSecureToken(length int) (string, error) {
	b := make([]byte, length)
	if _, err := rand.Read(b); err != nil {
		return "", err
	}
	return hex.EncodeToString(b), nil
}
