package aes

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand" // 用于生成随机 Nonce
	"fmt"
	"io"
)

// EncryptAESGCM 使用 AES-GCM 模式进行加密，并返回 Nonce + 密文的编码字符串。
// associatedData 是可选的关联数据，不加密但会被认证。
func EncryptAESGCM(plaintext, key, associatedData []byte) ([]byte, error) {
	// 检查密钥长度
	if len(key) != 16 && len(key) != 24 && len(key) != 32 {
		return nil, fmt.Errorf("AES key must be 16, 24, or 32 bytes long for AES-128, AES-192, or AES-256 respectively, got %d", len(key))
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher block: %w", err)
	}

	// NewGCM 包装了 AES 块，使其成为 GCM 模式的 AEAD
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Erro<PERSON>("failed to create GCM cipher: %w", err)
	}

	// Nonce 是一个随机值，每次加密必须不同，不需要保密。
	// GCM 的 Nonce 长度通常是 12 字节 (推荐)。
	nonce := make([]byte, aesGCM.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Seal 函数进行加密和认证。
	// 第一个参数是可选的 dst，如果为 nil，则会创建一个新的字节切片。
	// 第二个参数是 nonce。
	// 第三个参数是 plaintext。
	// 第四个参数是 associatedData (AAD)，不加密但参与认证。
	ciphertext := aesGCM.Seal(nil, nonce, plaintext, associatedData)

	// 将 Nonce 和密文拼接起来，通常 Nonce 放在前面
	// 这样解密时可以从密文前部提取 Nonce
	result := make([]byte, len(nonce)+len(ciphertext))
	copy(result[:len(nonce)], nonce)
	copy(result[len(nonce):], ciphertext)

	return result, nil
}

// DecryptAESGCM 对 Nonce + 密文进行解密，使用 AES-GCM 模式。
// associatedData 必须与加密时使用的关联数据完全相同。
func DecryptAESGCM(encryptedData, key, associatedData []byte) ([]byte, error) {
	// 检查密钥长度
	if len(key) != 16 && len(key) != 24 && len(key) != 32 {
		return nil, fmt.Errorf("AES key must be 16, 24, or 32 bytes long for AES-128, AES-192, or AES-256 respectively, got %d", len(key))
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create AES cipher block: %w", err)
	}

	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM cipher: %w", err)
	}

	nonceSize := aesGCM.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("encrypted data too short to contain nonce")
	}

	// 从拼接的数据中分离 Nonce 和实际密文
	nonce, ciphertext := encryptedData[:nonceSize], encryptedData[nonceSize:]

	// Open 函数进行解密和认证。
	// 如果数据被篡改或密钥/Nonce 不匹配，Open 将返回错误。
	plaintext, err := aesGCM.Open(nil, nonce, ciphertext, associatedData)
	if err != nil {
		// 这里的错误通常是 "cipher: message authentication failed"
		return nil, fmt.Errorf("failed to decrypt or authenticate data: %w", err)
	}

	return plaintext, nil
}
