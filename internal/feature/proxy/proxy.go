package proxy

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	ctx "context"
	"crypto/tls"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/andybalholm/brotli"
	"github.com/google/uuid"
	iriszap "github.com/iris-contrib/middleware/zap"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/context"
	"github.com/kataras/iris/v12/core/host"
	"github.com/klauspost/compress/zstd"
	"go.uber.org/zap"
	"golang.org/x/net/html"

	"fn_helper/internal/feature/config"
)

type WebProxy struct {
	rule config.ProxyRule // 代理配置

	log     *zap.Logger
	app     *iris.Application // 内部web代理服务
	started bool              // 代理是否已经启动
	lock    sync.RWMutex
}

// NewWebProxy 新建WEB代理，唯一入口
func NewWebProxy(rule config.ProxyRule) *WebProxy {
	rule.Id = uuid.NewString()
	return &WebProxy{
		rule: rule,
		log:  zap.L().Named("proxy"), //不同端口的代理不一样
	}
}

func (w *WebProxy) GetRule() config.ProxyRule {
	return w.rule
}

func (w *WebProxy) GetId() string {
	return w.rule.Id
}

func (w *WebProxy) IsRunning() bool {
	w.lock.RLock()
	defer w.lock.RUnlock()
	return w.started
}

func (w *WebProxy) Start() error {
	w.lock.Lock()
	defer w.lock.Unlock()
	return w.startInternal()
}

func (w *WebProxy) Stop() error {
	w.lock.Lock()
	defer w.lock.Unlock()
	return w.stopInternal()
}

func (w *WebProxy) Restart() error {
	w.lock.Lock()
	defer w.lock.Unlock()

	w.log.Info("restarting proxy...", zap.Int("port", w.rule.ListenPort))
	if err := w.stopInternal(); err != nil {
		w.log.Error("failed to stop proxy during restart", zap.Error(err))
		return fmt.Errorf("failed to stop proxy for restart: %w", err)
	}

	if err := w.startInternal(); err != nil {
		w.log.Error("failed to start proxy during restart", zap.Error(err))
		return fmt.Errorf("failed to start proxy for restart: %w", err)
	}

	w.log.Info("proxy restarted successfully", zap.Int("port", w.rule.ListenPort))
	return nil
}

// stopInternal is the non-locking version of Stop for internal use.
func (w *WebProxy) stopInternal() error {
	if !w.started || w.app == nil {
		return errors.New("proxy not started or not initialized")
	}

	c, cancel := ctx.WithTimeout(ctx.Background(), 10*time.Second)
	defer cancel()

	err := w.app.Shutdown(c)
	if err != nil {
		w.log.Error("proxy shutdown failed", zap.Error(err))
		return err
	}

	w.started = false
	w.log.Info("proxy stopped successfully", zap.Int("port", w.rule.ListenPort))
	return nil
}

// startInternal is the non-locking version of Start for internal use.
func (w *WebProxy) startInternal() error {
	if w.started {
		return nil
	}

	// 检查代理是否启用
	if !w.rule.Enabled {
		w.log.Info("代理已禁用，跳过启动", zap.String("id", w.rule.Id), zap.Int("port", w.rule.ListenPort))
		return nil
	}

	addr := fmt.Sprintf(":%d", w.rule.ListenPort)

	// 检查是否已经初始化
	if w.app == nil {
		w.app = iris.New().Configure(iris.NonBlocking())
		w.app.Use(iriszap.New(zap.L(), time.RFC3339, false))
		w.app.Use(iriszap.RecoveryWithZap(zap.L(), true))

		for _, loc := range w.rule.Locations {
			// 只为启用的 Location 创建代理
			if !loc.Enabled {
				w.log.Info("跳过禁用的 Location", zap.String("path", loc.Path))
				continue
			}

			err := w.CreateProxy(w.rule.ListenHost, loc)
			if err != nil {
				w.log.Error("Create proxy failed", zap.Error(err), zap.String("add", addr), zap.Any("rule", w.rule))
			}
		}
	}

	// 检查是否需要TLS
	useTLS := false
	var tlsCert, tlsKey string
	if w.rule.SSLCertificate != "" && w.rule.SSLCertificateKey != "" {
		useTLS = true
		tlsCert = w.rule.SSLCertificate
		tlsKey = w.rule.SSLCertificateKey
	}

	if useTLS {
		zap.L().Info("启动HTTPS代理服务", zap.String("address", addr))
		w.started = true
		err := w.app.Run(iris.TLS(addr, tlsCert, tlsKey))
		if err != nil {
			zap.L().Error("HTTPS代理服务启动失败", zap.String("address", addr), zap.Error(err))
			w.started = false
		}
		return err
	}

	zap.L().Info("启动HTTP代理服务", zap.String("address", addr))
	w.started = true
	err := w.app.Run(iris.Addr(addr))
	if err != nil {
		zap.L().Error("HTTP代理服务启动失败", zap.String("address", addr), zap.Error(err))
		w.started = false
	}

	return err
}

func (w *WebProxy) StartProxyLocation(id string, loc config.ProxyLocation) error {
	w.lock.Lock()
	defer w.lock.Unlock()

	if w.rule.Id != id {
		return fmt.Errorf("代理ID不匹配：请求ID %s，当前代理ID %s", id, w.rule.Id)
	}

	// 检查是否已存在相同路径的 Location
	for _, existingLoc := range w.rule.Locations {
		if existingLoc.Path == loc.Path {
			return fmt.Errorf("已存在相同路径的代理Location: %s", loc.Path)
		}
	}

	// 添加新的 Location
	w.rule.Locations = append(w.rule.Locations, loc)

	w.log.Info("添加新的代理Location，触发代理更新", zap.String("path", loc.Path))
	return w.updateProxyInternal(w.rule)
}

func (w *WebProxy) StopProxyLocation(id string, loc config.ProxyLocation) error {
	w.lock.Lock()
	defer w.lock.Unlock()

	if w.rule.Id != id {
		return fmt.Errorf("代理ID不匹配：请求ID %s，当前代理ID %s", id, w.rule.Id)
	}

	// 查找并移除 Location
	found := false
	newLocations := []config.ProxyLocation{}
	for _, existingLoc := range w.rule.Locations {
		if existingLoc.Path == loc.Path {
			found = true
			continue // 跳过此 Location，实现“停止”或“删除”
		}
		newLocations = append(newLocations, existingLoc)
	}

	if !found {
		return fmt.Errorf("未找到指定路径的代理Location: %s", loc.Path)
	}

	w.rule.Locations = newLocations

	w.log.Info("停止/删除代理Location，触发代理更新", zap.String("path", loc.Path))
	return w.updateProxyInternal(w.rule)
}

func (w *WebProxy) UpdateProxyLocation(id string, loc config.ProxyLocation) error {
	w.lock.Lock()
	defer w.lock.Unlock()

	if w.rule.Id != id {
		return fmt.Errorf("代理ID不匹配：请求ID %s，当前代理ID %s", id, w.rule.Id)
	}

	// 查找并更新 Location
	found := false
	for i, existingLoc := range w.rule.Locations {
		if existingLoc.Path == loc.Path {
			w.rule.Locations[i] = loc // 更新
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("未找到指定路径的代理Location进行更新: %s", loc.Path)
	}

	w.log.Info("更新代理Location，触发代理更新", zap.String("path", loc.Path))
	return w.updateProxyInternal(w.rule)
}

func (w *WebProxy) DeleteProxyLocation(id string, loc config.ProxyLocation) error {
	// 在当前实现中，StopProxyLocation 和 DeleteProxyLocation 行为一致，都将 Location 从配置中移除
	return w.StopProxyLocation(id, loc)
}

func (w *WebProxy) UpdateProxy(newRule config.ProxyRule) error {
	w.lock.Lock()
	defer w.lock.Unlock()
	return w.updateProxyInternal(newRule)
}

// updateProxyInternal 是 UpdateProxy 的内部版本，不获取锁，用于在已持有锁的情况下调用
func (w *WebProxy) updateProxyInternal(newRule config.ProxyRule) error {
	w.log.Info("updating proxy...", zap.Int("old_port", w.rule.ListenPort), zap.Int("new_port", newRule.ListenPort))

	// 1. Stop the existing proxy if it's running
	if w.started {
		if err := w.stopInternal(); err != nil {
			w.log.Error("failed to stop proxy during update", zap.Error(err))
			return fmt.Errorf("failed to stop proxy for update: %w", err)
		}
	}

	// 2. Update the rule
	w.rule = newRule
	// Ensure the ID is set if it's new
	if len(w.rule.Id) < 1 {
		w.rule.Id = uuid.NewString()
	}

	// 3. Reset the application to force re-initialization of routes
	w.app = nil

	// 4. Start the proxy with the new rule
	if err := w.startInternal(); err != nil {
		w.log.Error("failed to start proxy during update", zap.Error(err))
		return fmt.Errorf("failed to start proxy for update: %w", err)
	}

	w.log.Info("proxy updated successfully", zap.Int("new_port", w.rule.ListenPort))
	return nil
}

func (w *WebProxy) StopProxy(id string) error {
	w.lock.Lock()
	defer w.lock.Unlock()

	// 检查传入的ID是否与当前代理的ID匹配
	if w.rule.Id != id {
		return fmt.Errorf("代理ID不匹配：请求ID %s，当前代理ID %s", id, w.rule.Id)
	}

	// 如果代理未启动，则无需操作
	if !w.started {
		w.log.Info("代理已停止，无需重复停止", zap.String("id", id))
		return nil
	}

	w.log.Info("正在停止代理...", zap.String("id", id), zap.Int("port", w.rule.ListenPort))

	// 停止内部的Iris应用
	if err := w.stopInternal(); err != nil {
		w.log.Error("停止代理失败", zap.Error(err))
		return fmt.Errorf("停止代理失败: %w", err)
	}

	w.log.Info("代理已成功停止", zap.String("id", id))
	return nil
}

func (w *WebProxy) DeleteProxy(id string) error {
	w.lock.Lock()
	defer w.lock.Unlock()

	// 检查传入的ID是否与当前代理的ID匹配
	if w.rule.Id != id {
		return fmt.Errorf("代理ID不匹配：请求ID %s，当前代理ID %s", id, w.rule.Id)
	}

	// 如果代理未启动，则无需操作
	if !w.started {
		w.log.Info("代理已停止，无需删除操作", zap.String("id", id))
		return nil
	}

	w.log.Info("正在停止代理以便删除...", zap.String("id", id), zap.Int("port", w.rule.ListenPort))

	// 停止内部的Iris应用
	if err := w.stopInternal(); err != nil {
		w.log.Error("删除过程中停止代理失败", zap.Error(err))
		return fmt.Errorf("删除代理时停止失败: %w", err)
	}

	// 释放Iris应用实例，使其可以被垃圾回收，并确保不再使用
	w.app = nil

	w.log.Info("代理已成功停止并标记为删除", zap.String("id", id))
	return nil
}

func (w *WebProxy) CreateProxy(listenHost string, loc config.ProxyLocation) error {
	// 如果配置了 Root，则创建静态文件服务
	if loc.Root != "" {
		return w.createStaticFileServer(loc)
	}

	// 如果配置了 ProxyPass，则创建反向代理
	if loc.ProxyPass != "" {
		// 普通的反向代理
		if len(loc.Path) == 0 || loc.Path == "/" {
			return w.createProxy(loc)
		}
		// 基于路径的反向代理，带有特殊逻辑
		return w.createPathProxy(listenHost, loc)
	}

	return fmt.Errorf("location配置错误：必须配置 ProxyPass 或 Root 中的一个")
}

// 新建一般的代理服务，监听path为空或者"/"
func (w *WebProxy) createProxy(loc config.ProxyLocation) error {
	targetURL, err := url.Parse(loc.ProxyPass)
	if err != nil {
		zap.L().Error("无效的目标URL", zap.String("url", loc.ProxyPass), zap.Error(err))
		return err
	}

	p := host.ProxyHandler(targetURL, &tls.Config{InsecureSkipVerify: loc.InsecureSkipVerify})
	p.Director = func(req *http.Request) {
		rewriteRequestURL(req, targetURL)

		//设置自定义请求头
		for key, value := range loc.ProxySetHeader {
			req.Header.Set(key, value)
		}
	}

	// 注册路径
	party := w.app.Party(loc.Path)
	party.Any("/**", func(ctx *context.Context) {
		p.ServeHTTP(ctx.ResponseWriter(), ctx.Request())
	})

	return nil
}

// createStaticFileServer 创建静态文件服务
func (w *WebProxy) createStaticFileServer(loc config.ProxyLocation) error {
	// 注册静态文件服务路径
	party := w.app.Party(loc.Path)

	// 使用 Iris 的静态文件服务功能
	party.HandleDir("/", iris.Dir(loc.Root))

	w.log.Info("创建静态文件服务",
		zap.String("path", loc.Path),
		zap.String("root", loc.Root),
		zap.String("name", loc.Name))

	return nil
}

func (w *WebProxy) createPathProxy(listenHost string, loc config.ProxyLocation) error {
	targetURL, err := url.Parse(loc.ProxyPass)
	if err != nil {
		zap.L().Error("无效的目标URL", zap.String("url", loc.ProxyPass), zap.Error(err))
		return err
	}

	proxyListenAddr := ":" + strconv.Itoa(w.rule.ListenPort)
	proxyBaseURL := listenHost + proxyListenAddr + loc.Path
	p := host.ProxyHandler(targetURL, &tls.Config{InsecureSkipVerify: loc.InsecureSkipVerify})
	p.Director = func(req *http.Request) {
		rewriteRequestURL(req, targetURL)
		// 移除代理前缀，转发给目标服务器
		req.URL.Path = strings.TrimPrefix(req.URL.Path, loc.Path)
		if req.URL.Path == "" {
			req.URL.Path = "/"
		} else if !strings.HasPrefix(req.URL.Path, "/") {
			req.URL.Path = strings.TrimSuffix("/"+req.URL.Path, "/")
		}

		// 传递真实的客户端IP
		if clientIP, _, err := net.SplitHostPort(req.RemoteAddr); err == nil {
			if prior := req.Header.Get("X-Forwarded-For"); prior != "" {
				req.Header.Set("X-Forwarded-For", prior+", "+clientIP)
			} else {
				req.Header.Set("X-Forwarded-For", clientIP)
			}
		}
		req.Header.Set("X-Real-IP", req.RemoteAddr)

		// 设置自定义请求头
		for key, value := range loc.ProxySetHeader {
			req.Header.Set(key, value)
		}

		// 动态校验请求是否为https
		//if req.TLS != nil || req.Header.Get("X-Forwarded-Proto") == "https" {
		//	req.Header.Set("X-Forwarded-Proto", "https") // 告诉后端原始请求是HTTPS
		//}
	}
	p.ModifyResponse = func(resp *http.Response) error {
		// 在这里添加重写 Location 头部逻辑
		if resp.StatusCode == 302 {
			location := resp.Header.Get("Location")
			if location != "" {
				parsedLocation, err := url.Parse(location)
				if err == nil {
					// 检查 Location 是否是相对路径（即不包含 Scheme 和 Host）
					// 并且是以 "/" 开头（表示相对根路径）
					// 并且不是一个绝对URL（例如 http://example.com/）
					if !parsedLocation.IsAbs() && strings.HasPrefix(parsedLocation.Path, "/") {
						// 如果是 /，则重定向到 /test
						// 如果是 /some/path，则重定向到 /test/some/path
						newLocation := strings.TrimSuffix(loc.Path+parsedLocation.Path, "/")
						resp.Header.Set("Location", newLocation)
						// fmt.Printf("Modified Location header from '%s' to '%s'\n", location, newLocation) // 调试信息
					} else if parsedLocation.IsAbs() && parsedLocation.Host == targetURL.Host {
						// 如果是目标服务器的绝对URL，也进行替换
						// 例如：http://*************:10088/ -> http://localhost:8080/test/
						newLocation := strings.TrimSuffix(strings.Replace(location, loc.ProxyPass, proxyBaseURL, 1), "/")
						resp.Header.Set("Location", newLocation)
					}
				}
			}
		}

		contentType := resp.Header.Get("Content-Type")
		if !strings.Contains(contentType, "text/html") {
			return nil
		}

		// 保存原始的压缩编码类型
		originalEncoding := resp.Header.Get("Content-Encoding")

		// 临时移除压缩相关的头部，稍后会重新设置
		resp.Header.Del("Content-Encoding")
		resp.Header.Del("Transfer-Encoding")

		// 读取原始响应体
		bodyBytes, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %w", err)
		}
		resp.Body.Close()

		// 如果原始响应有压缩编码，先解压缩
		var htmlContent []byte
		if originalEncoding != "" {
			htmlContent, err = decompressContent(bodyBytes, originalEncoding)
			if err != nil {
				w.log.Warn("failed to decompress content, using original",
					zap.String("encoding", originalEncoding),
					zap.Error(err))
				htmlContent = bodyBytes
			}
		} else {
			htmlContent = bodyBytes
		}

		// 解析HTML
		doc, err := html.Parse(bytes.NewReader(htmlContent))
		if err != nil {
			return fmt.Errorf("failed to parse HTML: %w", err)
		}

		var f func(*html.Node)
		f = func(n *html.Node) {
			if n.Type == html.ElementNode {
				// 查找并修改 <meta property="og:url"> 标签
				if n.Data == "meta" {
					for i, a := range n.Attr {
						if a.Key == "content" && strings.Contains(a.Val, loc.ProxyPass) {
							// 修改 content 属性的值为代理的 baseURL
							// 假设 proxyBaseURL 已经包含了 '/test' 前缀
							// 例如：http://localhost:8080/test
							n.Attr[i].Val = proxyBaseURL + strings.TrimPrefix(n.Attr[i].Val, loc.ProxyPass)
							// 解释：
							// original og:url content: http://*************:10088/some/path
							// after TrimPrefix: /some/path
							// new og:url content: http://localhost:8080/test/some/path
						}
					}
				}

				// 处理内联脚本中的动态导入和模块路径
				if n.Data == "script" && n.FirstChild != nil && n.FirstChild.Type == html.TextNode {
					scriptContent := n.FirstChild.Data
					modifiedScript := rewriteScriptPaths(scriptContent, loc.Path)
					if modifiedScript != scriptContent {
						n.FirstChild.Data = modifiedScript
					}
				}

				for i, a := range n.Attr {
					if a.Key == "src" || a.Key == "href" || a.Key == "action" {
						originalPath := a.Val
						newPath := originalPath
						modified := false

						// 跳过外部链接、协议相对链接、数据URI、锚点等
						if shouldSkipPath(originalPath) {
							continue
						}

						// 处理绝对路径（以 / 开头但不是 // 开头）
						if strings.HasPrefix(originalPath, "/") && !strings.HasPrefix(originalPath, "//") {
							// 检查是否已经有代理前缀
							if !strings.HasPrefix(originalPath, loc.Path+"/") && originalPath != loc.Path {
								newPath = loc.Path + originalPath
								modified = true
							}
						} else if strings.HasPrefix(originalPath, loc.ProxyPass) {
							// 处理目标服务器的绝对URL
							relativePath := strings.TrimPrefix(originalPath, loc.ProxyPass)
							if strings.HasPrefix(relativePath, "/") {
								newPath = loc.Path + relativePath
							} else {
								newPath = loc.Path + "/" + relativePath
							}
							modified = true
						} else if !strings.Contains(originalPath, "://") && !strings.HasPrefix(originalPath, "#") {
							// 处理相对路径（不包含协议且不是锚点）
							// 对于相对路径，更积极地添加前缀，特别是对于静态资源
							if isStaticResource(originalPath) || looksLikeAsset(originalPath) {
								newPath = loc.Path + "/" + originalPath
								modified = true
							}
						}

						if modified {
							n.Attr[i].Val = newPath
						}
					}
				}
			}

			for c := n.FirstChild; c != nil; c = c.NextSibling {
				f(c)
			}
		}

		f(doc)

		// 将修改后的 HTML 重新渲染回字节流
		var buf bytes.Buffer
		if err := html.Render(&buf, doc); err != nil {
			return fmt.Errorf("failed to render modified HTML: %w", err)
		}

		// 获取修改后的内容
		modifiedContent := buf.Bytes()

		// 如果原始响应有压缩编码，重新压缩修改后的内容
		if originalEncoding != "" {
			compressedContent, err := compressContent(modifiedContent, originalEncoding)
			if err != nil {
				// 如果压缩失败，记录错误但继续使用未压缩的内容
				w.log.Warn("failed to recompress content, using uncompressed",
					zap.String("encoding", originalEncoding),
					zap.Error(err))
			} else {
				// 压缩成功，使用压缩后的内容并恢复压缩头部
				modifiedContent = compressedContent
				resp.Header.Set("Content-Encoding", originalEncoding)
			}
		}

		// 用修改后的内容替换原始响应体，并更新 Content-Length
		resp.ContentLength = int64(len(modifiedContent))
		resp.Header.Set("Content-Length", fmt.Sprint(len(modifiedContent)))
		resp.Body = io.NopCloser(bytes.NewReader(modifiedContent))

		return nil
	}

	party := w.app.Party(loc.Path)
	party.Any("/", func(ctx *context.Context) {
		p.ServeHTTP(ctx.ResponseWriter(), ctx.Request())
	})
	party.Any("/{path:path}", func(ctx *context.Context) {
		p.ServeHTTP(ctx.ResponseWriter(), ctx.Request())
	})

	return nil
}

func rewriteRequestURL(req *http.Request, target *url.URL) {
	targetQuery := target.RawQuery
	req.URL.Scheme = target.Scheme
	req.URL.Host = target.Host
	req.URL.Path, req.URL.RawPath = joinURLPath(target, req.URL)
	if targetQuery == "" || req.URL.RawQuery == "" {
		req.URL.RawQuery = targetQuery + req.URL.RawQuery
	} else {
		req.URL.RawQuery = targetQuery + "&" + req.URL.RawQuery
	}
}

func joinURLPath(a, b *url.URL) (path, rawpath string) {
	if a.RawPath == "" && b.RawPath == "" {
		return singleJoiningSlash(a.Path, b.Path), ""
	}
	// Same as singleJoiningSlash, but uses EscapedPath to determine
	// whether a slash should be added
	apath := a.EscapedPath()
	bpath := b.EscapedPath()

	aslash := strings.HasSuffix(apath, "/")
	bslash := strings.HasPrefix(bpath, "/")

	switch {
	case aslash && bslash:
		return a.Path + b.Path[1:], apath + bpath[1:]
	case !aslash && !bslash:
		return a.Path + "/" + b.Path, apath + "/" + bpath
	}
	return a.Path + b.Path, apath + bpath
}

func singleJoiningSlash(a, b string) string {
	aslash := strings.HasSuffix(a, "/")
	bslash := strings.HasPrefix(b, "/")
	switch {
	case aslash && bslash:
		return a + b[1:]
	case !aslash && !bslash:
		return a + "/" + b
	}
	return a + b
}

// decompressContent 根据指定的编码类型解压缩内容
func decompressContent(content []byte, encoding string) ([]byte, error) {
	switch encoding {
	case "gzip":
		reader, err := gzip.NewReader(bytes.NewReader(content))
		if err != nil {
			return nil, fmt.Errorf("failed to create gzip reader: %w", err)
		}
		defer reader.Close()

		decompressed, err := io.ReadAll(reader)
		if err != nil {
			return nil, fmt.Errorf("failed to read gzip content: %w", err)
		}
		return decompressed, nil

	case "deflate":
		reader := flate.NewReader(bytes.NewReader(content))
		defer reader.Close()

		decompressed, err := io.ReadAll(reader)
		if err != nil {
			return nil, fmt.Errorf("failed to read deflate content: %w", err)
		}
		return decompressed, nil

	case "br":
		reader := brotli.NewReader(bytes.NewReader(content))
		decompressed, err := io.ReadAll(reader)
		if err != nil {
			return nil, fmt.Errorf("failed to read brotli content: %w", err)
		}
		return decompressed, nil

	case "zstd":
		decoder, err := zstd.NewReader(bytes.NewReader(content))
		if err != nil {
			return nil, fmt.Errorf("failed to create zstd decoder: %w", err)
		}
		defer decoder.Close()

		decompressed, err := io.ReadAll(decoder)
		if err != nil {
			return nil, fmt.Errorf("failed to read zstd content: %w", err)
		}
		return decompressed, nil

	default:
		// 没有压缩或不支持的压缩类型，返回原始内容
		return content, nil
	}
}

// compressContent 根据指定的编码类型压缩内容
// 支持主流的压缩格式：gzip, deflate, br (brotli), zstd
func compressContent(content []byte, encoding string) ([]byte, error) {
	var buf bytes.Buffer

	switch encoding {
	case "gzip":
		writer := gzip.NewWriter(&buf)
		if _, err := writer.Write(content); err != nil {
			return nil, fmt.Errorf("failed to write gzip content: %w", err)
		}
		if err := writer.Close(); err != nil {
			return nil, fmt.Errorf("failed to close gzip writer: %w", err)
		}

	case "deflate":
		writer, err := flate.NewWriter(&buf, flate.DefaultCompression)
		if err != nil {
			return nil, fmt.Errorf("failed to create deflate writer: %w", err)
		}
		if _, err := writer.Write(content); err != nil {
			return nil, fmt.Errorf("failed to write deflate content: %w", err)
		}
		if err := writer.Close(); err != nil {
			return nil, fmt.Errorf("failed to close deflate writer: %w", err)
		}

	case "br":
		writer := brotli.NewWriter(&buf)
		if _, err := writer.Write(content); err != nil {
			return nil, fmt.Errorf("failed to write brotli content: %w", err)
		}
		if err := writer.Close(); err != nil {
			return nil, fmt.Errorf("failed to close brotli writer: %w", err)
		}

	case "zstd":
		encoder, err := zstd.NewWriter(&buf)
		if err != nil {
			return nil, fmt.Errorf("failed to create zstd encoder: %w", err)
		}
		if _, err := encoder.Write(content); err != nil {
			encoder.Close()
			return nil, fmt.Errorf("failed to write zstd content: %w", err)
		}
		if err := encoder.Close(); err != nil {
			return nil, fmt.Errorf("failed to close zstd encoder: %w", err)
		}

	default:
		// 不支持的压缩类型，返回原始内容
		return content, nil
	}

	return buf.Bytes(), nil
}

// shouldSkipPath 判断是否应该跳过路径重写
func shouldSkipPath(path string) bool {
	// 跳过空路径
	if path == "" {
		return true
	}

	// 跳过外部链接（包含协议）
	if strings.Contains(path, "://") {
		return true
	}

	// 跳过协议相对链接（以 // 开头）
	if strings.HasPrefix(path, "//") {
		return true
	}

	// 跳过数据URI
	if strings.HasPrefix(path, "data:") {
		return true
	}

	// 跳过锚点链接
	if strings.HasPrefix(path, "#") {
		return true
	}

	// 跳过 mailto、tel 等特殊协议
	if strings.HasPrefix(path, "mailto:") || strings.HasPrefix(path, "tel:") ||
	   strings.HasPrefix(path, "javascript:") || strings.HasPrefix(path, "blob:") {
		return true
	}

	return false
}

// isStaticResource 判断路径是否看起来像静态资源
func isStaticResource(path string) bool {
	// 常见的静态资源扩展名
	staticExtensions := []string{
		".js", ".css", ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico",
		".woff", ".woff2", ".ttf", ".eot", ".otf", ".mp4", ".webm", ".mp3",
		".pdf", ".zip", ".json", ".xml", ".txt", ".map",
	}

	// 转换为小写进行比较
	lowerPath := strings.ToLower(path)

	// 检查是否以静态资源扩展名结尾
	for _, ext := range staticExtensions {
		if strings.HasSuffix(lowerPath, ext) {
			return true
		}
	}

	// 检查是否包含常见的静态资源目录
	staticDirs := []string{
		"/assets/", "/static/", "/public/", "/dist/", "/build/",
		"/js/", "/css/", "/img/", "/images/", "/fonts/", "/media/",
	}

	for _, dir := range staticDirs {
		if strings.Contains(lowerPath, dir) {
			return true
		}
	}

	return false
}

// looksLikeAsset 判断路径是否看起来像现代前端构建工具生成的资源文件
func looksLikeAsset(path string) bool {
	// 跳过明显不是资源的路径
	if strings.Contains(path, "?") || strings.Contains(path, "=") {
		return false
	}

	// 检查是否包含哈希模式（常见于现代前端构建工具）
	// 例如：main-abc123.js, style.def456.css, Vting-D9EVcE-h.js
	hashPatterns := []string{
		"-", "_", ".", // 哈希分隔符
	}

	lowerPath := strings.ToLower(path)

	// 如果文件名包含哈希模式且有合适的扩展名
	for _, pattern := range hashPatterns {
		if strings.Contains(path, pattern) {
			// 检查是否以常见的资源扩展名结尾
			if strings.HasSuffix(lowerPath, ".js") ||
			   strings.HasSuffix(lowerPath, ".css") ||
			   strings.HasSuffix(lowerPath, ".mjs") ||
			   strings.HasSuffix(lowerPath, ".ts") ||
			   strings.HasSuffix(lowerPath, ".jsx") ||
			   strings.HasSuffix(lowerPath, ".tsx") {
				return true
			}
		}
	}

	// 检查是否是无扩展名但看起来像资源的文件
	// 例如：一些现代应用可能有无扩展名的资源文件
	if !strings.Contains(path, ".") && len(path) > 3 {
		// 如果路径较短且包含常见的资源文件模式
		if strings.Contains(strings.ToLower(path), "chunk") ||
		   strings.Contains(strings.ToLower(path), "vendor") ||
		   strings.Contains(strings.ToLower(path), "runtime") {
			return true
		}
	}

	return false
}

// rewriteScriptPaths 重写内联脚本中的路径
func rewriteScriptPaths(script, pathPrefix string) string {
	// 处理动态导入：import("./module.js") 或 import('/assets/module.js')
	result := script

	// 查找并替换 import("...") 模式
	for {
		start := strings.Index(result, `import("`)
		if start == -1 {
			start = strings.Index(result, `import('`)
			if start == -1 {
				break
			}
		}

		// 找到引号的位置
		quoteChar := result[start+7] // " 或 '
		quoteStart := start + 8
		quoteEnd := strings.Index(result[quoteStart:], string(quoteChar))
		if quoteEnd == -1 {
			break
		}
		quoteEnd += quoteStart

		// 提取路径
		originalPath := result[quoteStart:quoteEnd]

		// 重写路径
		newPath := rewriteSinglePath(originalPath, pathPrefix)

		// 替换
		if newPath != originalPath {
			result = result[:quoteStart] + newPath + result[quoteEnd:]
		} else {
			// 如果没有修改，跳过这个匹配以避免无限循环
			result = result[:start] + "IMPORT_PROCESSED" + result[start+6:]
		}
	}

	// 恢复被标记的import
	result = strings.ReplaceAll(result, "IMPORT_PROCESSED", "import")

	return result
}

// rewriteSinglePath 重写单个路径
func rewriteSinglePath(path, pathPrefix string) string {
	// 跳过不需要重写的路径
	if shouldSkipPath(path) {
		return path
	}

	// 处理绝对路径
	if strings.HasPrefix(path, "/") && !strings.HasPrefix(path, "//") {
		// 检查是否已经有代理前缀
		if !strings.HasPrefix(path, pathPrefix+"/") && path != pathPrefix {
			return pathPrefix + path
		}
	} else if !strings.Contains(path, "://") && !strings.HasPrefix(path, "#") {
		// 处理相对路径
		if isStaticResource(path) || looksLikeAsset(path) {
			return pathPrefix + "/" + path
		}
	}

	return path
}
