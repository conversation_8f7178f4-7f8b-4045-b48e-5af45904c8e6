package proxy

import (
	"errors"
	"fmt"
	"fn_helper/internal/feature/config"
	"go.uber.org/zap"
	"net"
	"net/url"
	"os"
	"regexp"
	"strings"
	"sync"
)

var (
	ErrProxyNotFound    = errors.New("代理不存在")
	ErrPortInUse        = errors.New("端口已被占用")
	ErrDuplicateId      = errors.New("代理ID重复")
	ErrLocationNotFound = errors.New("location不存在")
	ErrLocationExists   = errors.New("location已存在")
	ErrInvalidLocation  = errors.New("无效的location配置")

	// HTTP header name的合法字符规则：
	// 1. 只能包含ASCII字符
	// 2. 第一个字符必须是字母
	// 3. 其他字符可以是字母、数字、连字符(-)
	// 参考 RFC 7230
	httpHeaderNameRegex = regexp.MustCompile("^[A-Za-z][-A-Za-z0-9]*$")

	// 域名验证正则表达式
	// 支持标准域名格式，包括子域名
	hostnameRegex = regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
)

const (
	minPort = 1
	maxPort = 65535
)

// isValidHostname 验证主机名是否有效（支持IP地址和域名）
func isValidHostname(hostname string) bool {
	// 1. 检查是否为有效的IP地址
	if net.ParseIP(hostname) != nil {
		return true
	}

	// 2. 检查长度限制
	if len(hostname) == 0 || len(hostname) > 253 {
		return false
	}

	// 3. 检查是否为有效的域名格式
	if !hostnameRegex.MatchString(hostname) {
		return false
	}

	// 4. 检查每个标签的长度（域名的每个部分不能超过63个字符）
	labels := strings.Split(hostname, ".")
	for _, label := range labels {
		if len(label) == 0 || len(label) > 63 {
			return false
		}
	}

	return true
}

type ProxyManager struct {
	proxyMap map[string]*WebProxy
	lock     sync.RWMutex
}

func NewProxyManager() *ProxyManager {
	return &ProxyManager{
		proxyMap: make(map[string]*WebProxy),
		lock:     sync.RWMutex{},
	}
}

// Start 程序启动时执行
func (p *ProxyManager) Start() error {
	rules := config.Global().Proxy
	if len(rules) < 1 {
		return nil
	}

	// 预校验所有规则
	if err := p.validateRules(rules); err != nil {
		return err
	}

	// 并发启动所有代理
	errChan := make(chan error, len(rules))
	var wg sync.WaitGroup
	for _, rule := range rules {
		wg.Add(1)
		go func(rule config.ProxyRule) {
			defer wg.Done()

			proxy := NewWebProxy(rule)
			if err := proxy.Start(); err != nil {
				errChan <- fmt.Errorf("代理启动失败[%s]: %w", rule.Id, err)
				return
			}

			p.lock.Lock()
			p.proxyMap[proxy.GetId()] = proxy
			p.lock.Unlock()
		}(rule)
	}

	wg.Wait()
	close(errChan)

	// 检查是否有错误发生
	if len(errChan) > 0 {
		// 收集所有错误
		var errMsgs []string
		for err := range errChan {
			errMsgs = append(errMsgs, err.Error())
		}
		return fmt.Errorf("启动失败: %v", errMsgs)
	}

	return nil
}

// validateRules 校验代理规则
func (p *ProxyManager) validateRules(rules []config.ProxyRule) error {
	portsMap := make(map[int]string)    // port -> id mapping
	idsMap := make(map[string]struct{}) // id existence check

	for _, rule := range rules {
		// 检查端口冲突
		if existingId, ok := portsMap[rule.ListenPort]; ok {
			return fmt.Errorf("%w: 端口 %d 同时被代理 %s 和 %s 使用",
				ErrPortInUse, rule.ListenPort, existingId, rule.Id)
		}
		portsMap[rule.ListenPort] = rule.Id

		// 检查ID重复
		if len(rule.Id) > 0 {
			if _, ok := idsMap[rule.Id]; ok {
				return fmt.Errorf("%w: %s", ErrDuplicateId, rule.Id)
			}
			idsMap[rule.Id] = struct{}{}
		}
	}
	return nil
}

// validateProxyRule 验证代理规则的合法性
func (p *ProxyManager) validateProxyRule(rule config.ProxyRule) error {
	// 验证 Id 字段（新建时可以为空，会自动生成）
	if rule.Id != "" {
		rule.Id = strings.TrimSpace(rule.Id)
		if rule.Id == "" {
			return fmt.Errorf("id不能为空字符串")
		}
	}

	// 验证 Name 字段（可选字段，如果提供则不能为空字符串）
	if rule.Name != "" {
		rule.Name = strings.TrimSpace(rule.Name)
		if rule.Name == "" {
			return fmt.Errorf("name不能为空字符串")
		}
	}

	if rule.ListenPort < minPort || rule.ListenPort > maxPort {
		return fmt.Errorf("端口范围必须在 %d-%d 之间", minPort, maxPort)
	}

	if rule.ListenHost != "" {
		// 验证 ListenHost 是否为有效的IP地址或域名
		if !isValidHostname(rule.ListenHost) {
			return fmt.Errorf("无效的监听地址: %s", rule.ListenHost)
		}
	}

	// 校验 SSL 配置
	if rule.SSLCertificate != "" || rule.SSLCertificateKey != "" {
		if rule.SSLCertificate == "" || rule.SSLCertificateKey == "" {
			return fmt.Errorf("SSL证书和密钥必须同时配置")
		}
		// TODO: 可以进一步校验证书文件是否存在
	}

	// 校验 locations
	locPathMap := make(map[string]struct{})
	for _, loc := range rule.Locations {
		if err := p.validateLocation(loc); err != nil {
			return fmt.Errorf("location[%s]配置错误: %w", loc.Path, err)
		}

		if _, exists := locPathMap[loc.Path]; exists {
			return fmt.Errorf("location path重复: %s", loc.Path)
		}
		locPathMap[loc.Path] = struct{}{}
	}

	return nil
}

// validateLocation 验证location配置的合法性
func (p *ProxyManager) validateLocation(loc config.ProxyLocation) error {
	if loc.Path == "" {
		return fmt.Errorf("%w: path不能为空", ErrInvalidLocation)
	}
	if !strings.HasPrefix(loc.Path, "/") {
		return fmt.Errorf("%w: path必须以/开头", ErrInvalidLocation)
	}

	// 验证 Name 字段（可选字段，如果提供则不能为空字符串）
	if loc.Name != "" {
		loc.Name = strings.TrimSpace(loc.Name)
		if loc.Name == "" {
			return fmt.Errorf("%w: name不能为空字符串", ErrInvalidLocation)
		}
	}

	// 检查 root 和 proxy_pass 配置
	if loc.ProxyPass == "" && loc.Root == "" {
		return fmt.Errorf("%w: proxy_pass和root不能同时为空", ErrInvalidLocation)
	}
	if loc.ProxyPass != "" && loc.Root != "" {
		return fmt.Errorf("%w: proxy_pass和root不能同时配置", ErrInvalidLocation)
	}

	// 如果配置了 proxy_pass，验证其格式
	if loc.ProxyPass != "" {
		if !strings.HasPrefix(loc.ProxyPass, "http://") && !strings.HasPrefix(loc.ProxyPass, "https://") {
			return fmt.Errorf("%w: proxy_pass必须以http://或https://开头", ErrInvalidLocation)
		}
		// 解析URL以验证其合法性
		if _, err := url.Parse(loc.ProxyPass); err != nil {
			return fmt.Errorf("%w: proxy_pass格式无效: %s", ErrInvalidLocation, err)
		}
	}

	// 如果配置了 root，验证路径
	if loc.Root != "" {
		// 检查路径是否存在且可访问
		if _, err := os.Stat(loc.Root); err != nil {
			return fmt.Errorf("%w: root路径无效或不可访问: %s", ErrInvalidLocation, err)
		}
	}

	// 验证 allow_deny 配置
	if loc.AllowDeny != "" {
		if loc.AllowDeny != "allow" && loc.AllowDeny != "deny" {
			return fmt.Errorf("%w: allow_deny只能是allow或deny", ErrInvalidLocation)
		}
	}

	// 验证 proxy_set_header
	for key := range loc.ProxySetHeader {
		if key == "" {
			return fmt.Errorf("%w: proxy_set_header的key不能为空", ErrInvalidLocation)
		}
		// 检查header key的合法性
		if !httpHeaderNameRegex.MatchString(key) {
			return fmt.Errorf("%w: proxy_set_header含有无效的header名称: %s", ErrInvalidLocation, key)
		}
	}

	return nil
}

func (p *ProxyManager) QueryAll() map[string]*WebProxy {
	p.lock.RLock()
	defer p.lock.RUnlock()

	ret := make(map[string]*WebProxy, len(p.proxyMap))
	for k, v := range p.proxyMap {
		ret[k] = v
	}
	return ret
}

func (p *ProxyManager) NewProxy(rule config.ProxyRule) (*WebProxy, error) {
	// 先验证规则
	if err := p.validateProxyRule(rule); err != nil {
		return nil, fmt.Errorf("代理配置验证失败: %w", err)
	}

	p.lock.Lock()

	if _, exists := p.proxyMap[rule.Id]; exists {
		p.lock.Unlock()
		return nil, fmt.Errorf("%w: %s", ErrDuplicateId, rule.Id)
	}

	// 检查端口是否冲突
	for _, proxy := range p.proxyMap {
		if proxy.rule.ListenPort == rule.ListenPort {
			p.lock.Unlock()
			return nil, fmt.Errorf("%w: 端口 %d", ErrPortInUse, rule.ListenPort)
		}
	}

	// 释放锁后启动代理（避免在持有锁时进行耗时操作）
	p.lock.Unlock()

	// 尝试启动新代理
	newProxy := NewWebProxy(rule)
	if err := newProxy.Start(); err != nil {
		zap.L().Error("新代理启动失败", zap.Error(err), zap.String("id", rule.Id), zap.Int("port", newProxy.rule.ListenPort))
		return nil, fmt.Errorf("新代理启动失败: %w", err)
	}

	// 重新获取锁来添加到 map
	p.lock.Lock()

	p.proxyMap[rule.Id] = newProxy

	// 收集当前所有规则用于同步（在持有锁时）
	rules := make([]config.ProxyRule, 0, len(p.proxyMap))
	for _, proxy := range p.proxyMap {
		rules = append(rules, proxy.rule)
	}

	// 释放锁后进行同步
	p.lock.Unlock()
	if err := p.syncToConfigAndDiskInternal(rules); err != nil {
		// 启动成功但同步失败，记录警告
		zap.L().Warn("新代理已启动但配置同步失败", zap.Error(err), zap.String("id", rule.Id))
		return nil, fmt.Errorf("新代理启动成功但配置同步失败: %w", err)
	}
	// 注意：这里不能再使用 defer unlock，因为我们已经手动释放了锁

	zap.L().Info("成功创建并启动新代理", zap.String("id", rule.Id), zap.Int("port", newProxy.rule.ListenPort), zap.Int("locations", len(newProxy.rule.Locations)))
	return newProxy, nil
}

func (p *ProxyManager) UpdateProxy(rule config.ProxyRule) error {
	// 先验证更新的规则
	if err := p.validateProxyRule(rule); err != nil {
		return fmt.Errorf("代理配置验证失败: %w", err)
	}

	p.lock.Lock()

	id := rule.Id
	existingProxy, exists := p.proxyMap[id]
	if !exists {
		p.lock.Unlock()
		return fmt.Errorf("%w: %s", ErrProxyNotFound, id)
	}

	// 如果端口发生变化，检查新端口是否冲突
	if existingProxy.rule.ListenPort != rule.ListenPort {
		for _, pxy := range p.proxyMap {
			if pxy.GetId() != id && pxy.rule.ListenPort == rule.ListenPort {
				p.lock.Unlock()
				return fmt.Errorf("%w: 端口 %d", ErrPortInUse, rule.ListenPort)
			}
		}
	}

	// 释放锁后进行更新操作（避免在持有锁时进行耗时操作）
	p.lock.Unlock()

	if err := existingProxy.UpdateProxy(rule); err != nil {
		zap.L().Error("更新代理失败", zap.Error(err), zap.String("id", id))
		return fmt.Errorf("更新代理失败: %w", err)
	}

	// 重新获取锁来收集规则并同步
	p.lock.Lock()
	rules := make([]config.ProxyRule, 0, len(p.proxyMap))
	for _, proxy := range p.proxyMap {
		rules = append(rules, proxy.rule)
	}
	p.lock.Unlock()

	if err := p.syncToConfigAndDiskInternal(rules); err != nil {
		zap.L().Warn("代理已更新但配置同步失败",
			zap.Error(err),
			zap.String("id", id))
	}

	zap.L().Info("成功更新代理",
		zap.String("id", id),
		zap.Int("port", rule.ListenPort))
	return nil
}

// getProxy 获取代理实例，需要在调用处持有锁
func (p *ProxyManager) getProxy(id string) (*WebProxy, error) {
	proxy, exists := p.proxyMap[id]
	if !exists {
		return nil, fmt.Errorf("%w: %s", ErrProxyNotFound, id)
	}
	return proxy, nil
}

func (p *ProxyManager) DeleteProxy(id string) error {
	p.lock.Lock()

	proxy, err := p.getProxy(id)
	if err != nil {
		p.lock.Unlock()
		return err
	}

	port := proxy.rule.ListenPort

	// 释放锁后进行删除操作（避免在持有锁时进行耗时操作）
	p.lock.Unlock()

	// 调用WebProxy的DeleteProxy方法进行清理
	if err := proxy.DeleteProxy(id); err != nil {
		zap.L().Error("删除代理失败",
			zap.Error(err),
			zap.String("id", id),
			zap.Int("port", port))
		return fmt.Errorf("删除代理失败: %w", err)
	}

	// 重新获取锁来从 map 中删除并收集规则
	p.lock.Lock()
	delete(p.proxyMap, id)
	rules := make([]config.ProxyRule, 0, len(p.proxyMap))
	for _, proxy := range p.proxyMap {
		rules = append(rules, proxy.rule)
	}
	p.lock.Unlock()

	if err := p.syncToConfigAndDiskInternal(rules); err != nil {
		// 删除成功但同步失败，记录警告
		zap.L().Warn("代理已删除但配置同步失败",
			zap.Error(err),
			zap.String("id", id))
	}

	zap.L().Info("成功删除代理",
		zap.String("id", id),
		zap.Int("port", port))
	return nil
}

func (p *ProxyManager) StartProxy(id string) error {
	p.lock.RLock()
	proxy, err := p.getProxy(id)
	p.lock.RUnlock()

	if err != nil {
		return err
	}

	if err := proxy.Start(); err != nil {
		zap.L().Error("启动代理失败", zap.Error(err), zap.String("id", id))
		return fmt.Errorf("启动代理失败: %w", err)
	}

	zap.L().Info("成功启动代理",
		zap.String("id", id),
		zap.Int("port", proxy.rule.ListenPort))
	return nil
}

func (p *ProxyManager) StopProxy(id string) error {
	p.lock.RLock()
	proxy, err := p.getProxy(id)
	p.lock.RUnlock()

	if err != nil {
		return err
	}

	if err := proxy.Stop(); err != nil {
		zap.L().Error("停止代理失败", zap.Error(err), zap.String("id", id))
		return fmt.Errorf("停止代理失败: %w", err)
	}

	zap.L().Info("成功停止代理",
		zap.String("id", id),
		zap.Int("port", proxy.rule.ListenPort))
	return nil
}

// ProxyManager 管理所有 WebProxy 实例
// syncToConfigAndDisk 同步 proxyMap 到 config.Global().Proxy 并写入磁盘
func (p *ProxyManager) syncToConfigAndDisk() error {
	p.lock.RLock()
	rules := make([]config.ProxyRule, 0, len(p.proxyMap))
	for _, proxy := range p.proxyMap {
		rules = append(rules, proxy.rule)
	}
	p.lock.RUnlock()

	return p.syncToConfigAndDiskInternal(rules)
}

// syncToConfigAndDiskInternal 内部版本，不获取锁，用于在已持有锁的情况下调用
func (p *ProxyManager) syncToConfigAndDiskInternal(rules []config.ProxyRule) error {
	// 预校验所有规则
	if err := p.validateRules(rules); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	cfg := config.Global()
	cfg.Proxy = rules
	config.Replace(&cfg)
	if err := cfg.WriteToDisk(); err != nil {
		zap.L().Error("配置写入失败",
			zap.Error(err),
			zap.Int("rules_count", len(rules)))
		return fmt.Errorf("配置写入失败: %w", err)
	}

	return nil
}

func (p *ProxyManager) NewProxyLocation(proxyID string, loc config.ProxyLocation) error {
	if err := p.validateLocation(loc); err != nil {
		return err
	}

	p.lock.RLock()
	proxy, err := p.getProxy(proxyID)
	if err != nil {
		p.lock.RUnlock()
		return err
	}

	// 检查location是否已存在
	for _, existing := range proxy.rule.Locations {
		if existing.Path == loc.Path {
			p.lock.RUnlock()
			return fmt.Errorf("%w: path=%s", ErrLocationExists, loc.Path)
		}
	}
	p.lock.RUnlock()

	if err := proxy.StartProxyLocation(proxyID, loc); err != nil {
		zap.L().Error("添加代理Location失败",
			zap.Error(err),
			zap.String("proxy_id", proxyID),
			zap.String("path", loc.Path))
		return fmt.Errorf("添加代理Location失败: %w", err)
	}

	p.syncToConfigAndDisk()
	zap.L().Info("成功添加代理Location",
		zap.String("proxy_id", proxyID),
		zap.String("path", loc.Path))
	return nil
}

func (p *ProxyManager) UpdateProxyLocation(proxyID string, loc config.ProxyLocation) error {
	if err := p.validateLocation(loc); err != nil {
		return err
	}

	p.lock.RLock()
	proxy, err := p.getProxy(proxyID)
	if err != nil {
		p.lock.RUnlock()
		return err
	}

	// 检查location是否存在
	var exists bool
	for _, existing := range proxy.rule.Locations {
		if existing.Path == loc.Path {
			exists = true
			break
		}
	}
	if !exists {
		p.lock.RUnlock()
		return fmt.Errorf("%w: path=%s", ErrLocationNotFound, loc.Path)
	}
	p.lock.RUnlock()

	if err := proxy.UpdateProxyLocation(proxyID, loc); err != nil {
		zap.L().Error("更新代理Location失败",
			zap.Error(err),
			zap.String("proxy_id", proxyID),
			zap.String("path", loc.Path))
		return fmt.Errorf("更新代理Location失败: %w", err)
	}

	p.syncToConfigAndDisk()
	zap.L().Info("成功更新代理Location",
		zap.String("proxy_id", proxyID),
		zap.String("path", loc.Path))
	return nil
}

func (p *ProxyManager) DeleteProxyLocation(proxyID string, loc config.ProxyLocation) error {
	p.lock.RLock()
	proxy, exists := p.proxyMap[proxyID]
	p.lock.RUnlock()

	if !exists {
		return fmt.Errorf("代理ID不存在: %s", proxyID)
	}

	if err := proxy.DeleteProxyLocation(proxyID, loc); err != nil {
		zap.L().Error("删除代理Location失败", zap.Error(err), zap.String("proxy_id", proxyID), zap.String("path", loc.Path))
		return fmt.Errorf("删除代理Location失败: %w", err)
	}

	_ = p.syncToConfigAndDisk()
	zap.L().Info("成功删除代理Location", zap.String("proxy_id", proxyID), zap.String("path", loc.Path))
	return nil
}

func (p *ProxyManager) StartProxyLocation(proxyID string, loc config.ProxyLocation) error {
	p.lock.RLock()
	proxy, exists := p.proxyMap[proxyID]
	p.lock.RUnlock()

	if !exists {
		return fmt.Errorf("代理ID不存在: %s", proxyID)
	}

	// 调用WebProxy的StartProxyLocation方法
	if err := proxy.StartProxyLocation(proxyID, loc); err != nil {
		zap.L().Error("启动代理Location失败", zap.Error(err), zap.String("proxy_id", proxyID), zap.String("path", loc.Path))
		return fmt.Errorf("启动代理Location失败: %w", err)
	}

	zap.L().Info("成功启动代理Location", zap.String("proxy_id", proxyID), zap.String("path", loc.Path))
	return nil
}

func (p *ProxyManager) StopProxyLocation(proxyID string, loc config.ProxyLocation) error {
	p.lock.RLock()
	proxy, exists := p.proxyMap[proxyID]
	p.lock.RUnlock()

	if !exists {
		return fmt.Errorf("代理ID不存在: %s", proxyID)
	}

	// 调用WebProxy的StopProxyLocation方法
	if err := proxy.StopProxyLocation(proxyID, loc); err != nil {
		zap.L().Error("停止代理Location失败", zap.Error(err), zap.String("proxy_id", proxyID), zap.String("path", loc.Path))
		return fmt.Errorf("停止代理Location失败: %w", err)
	}

	zap.L().Info("成功停止代理Location", zap.String("proxy_id", proxyID), zap.String("path", loc.Path))
	return nil
}

func (p *ProxyManager) GetProxy(id string) (*WebProxy, error) {
	p.lock.RLock()
	defer p.lock.RUnlock()

	return p.getProxy(id)
}

func (p *ProxyManager) GetProxyLocation(proxyID string, path string) (*config.ProxyLocation, error) {
	if path == "" || !strings.HasPrefix(path, "/") {
		return nil, fmt.Errorf("%w: 无效的path", ErrInvalidLocation)
	}

	p.lock.RLock()
	proxy, err := p.getProxy(proxyID)
	if err != nil {
		p.lock.RUnlock()
		return nil, err
	}

	for _, loc := range proxy.rule.Locations {
		if loc.Path == path {
			locCopy := loc // 创建副本
			p.lock.RUnlock()
			return &locCopy, nil
		}
	}
	p.lock.RUnlock()

	return nil, fmt.Errorf("%w: proxyID=%s path=%s", ErrLocationNotFound, proxyID, path)
}
