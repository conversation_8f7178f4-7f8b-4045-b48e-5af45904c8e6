package main

import (
	"embed"
	"fmt"
	"io/fs"
	"os"
	"strings"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"

	"fn_helper/cmd/server"
	"fn_helper/internal/feature/config"
	"fn_helper/internal/session"
)

//go:embed dist
var embeddedFiles embed.FS

func main() {
	// 读取配置
	if err := config.Load(""); err != nil {
		panic(err)
	}
	appConfig := config.Global()
	// 将配置写入到文件系统中
	if err := appConfig.WriteToDisk(); err != nil {
		panic(err)
	}

	// 初始化 zap logger
	// 使用从配置中读取的日志路径
	zapLog, err := initLogger(appConfig.LogPath)
	if err != nil {
		panic(err)
	}
	defer zapLog.Sync()                  // 程序退出前，缓存区日志被写入到（日志或console）
	defer session.Global().StopCleanup() // Stop session cleanup goroutine on exit

	frontendFS, err := fs.Sub(embeddedFiles, "dist")
	if err != nil {
		zap.L().Panic("加载web文件失败", zap.Error(err))
	}

	serv := server.NewServer(zapLog, frontendFS)
	if err = serv.Start(); err != nil {
		zap.L().Panic("启动服务失败", zap.Error(err))
	}
}

// 初始化 Zap 日志器，支持文件切割
func initLogger(logPath string) (*zap.Logger, error) {
	logPath = strings.TrimSpace(logPath)
	if len(logPath) < 1 {
		logPath = "logs"
	}
	// 确保 logs 目录存在
	if _, err := os.Stat(logPath); os.IsNotExist(err) {
		if err := os.Mkdir(logPath, 0755); err != nil {
			return nil, fmt.Errorf("创建 %s 日志目录失败: %w", logPath, err)
		}
	}

	// 1. 配置 lumberjack 日志切割器
	// lumberjack.Logger 实现了 io.WriteCloser 接口，可以直接作为 Zap 的输出
	logWriter := &lumberjack.Logger{
		Filename:   logPath + "/app.log", // 日志文件路径
		MaxSize:    100,                  // 每个日志文件最大 100 MB
		MaxBackups: 7,                    // 最多保留 7 个备份文件 (旧文件)
		MaxAge:     7,                    // 最多保留 7 天的日志 (天数)
		Compress:   true,                 // 是否压缩旧文件 (gzip)
	}

	// 2. 新建ZAP默认日志配置
	zapConfig := zap.NewProductionEncoderConfig()
	zapConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	// 3. 创建一个多写入器，可以将日志同时写入文件和控制台
	// 这里将日志同时输出到 lumberjack writer 和 os.Stdout
	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(zapConfig), // 使用 JSON 格式编码器
		zapcore.NewMultiWriteSyncer(zapcore.AddSync(logWriter), zapcore.AddSync(os.Stdout)), // 同时写入文件和控制台
		zap.InfoLevel, // 设置最低日志级别为 Info
	)

	// 4. 构建 Zap 日志器
	zapLog := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zap.PanicLevel))

	// 5. 替换全局的 zap.L() 日志器
	zap.ReplaceGlobals(zapLog)

	return zapLog, nil
}
