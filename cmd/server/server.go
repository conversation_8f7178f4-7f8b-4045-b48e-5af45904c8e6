package server

import (
	"github.com/kataras/iris/v12/core/router"
	"io/fs"
	"net/http"
	"strconv"
	"time"

	"fn_helper/internal/api"
	"fn_helper/internal/feature/cert"
	"fn_helper/internal/feature/config"
	"fn_helper/internal/feature/proxy"
	"fn_helper/internal/session"
	iriszap "github.com/iris-contrib/middleware/zap"
	"github.com/kataras/iris/v12"
	"go.uber.org/zap"
)

type Server struct {
	app          *iris.Application
	log          *zap.Logger
	proxyManager *proxy.ProxyManager
	certManager  *cert.CertManager
	frontendFS   fs.FS
}

func NewServer(zapLog *zap.Logger, frontendFS fs.FS) *Server {
	return &Server{
		app:          iris.New(),
		log:          zapLog,
		certManager:  cert.NewCertManager(),
		proxyManager: proxy.NewProxyManager(),
		frontendFS:   frontendFS,
	}
}

func (s *Server) Start() error {
	if err := s.certManager.Start(); err != nil {
		return err
	}

	// 启动WEB代理
	if err := s.proxyManager.Start(); err != nil {
		return err
	}

	// loop
	if err := s.startHttpServer(); err != nil {
		return err
	}

	return nil
}

// 启动自身的管理服务
func (s *Server) startHttpServer() error {
	s.app.UseError()
	s.app.Use(iriszap.New(s.log, time.RFC3339, false))
	s.app.Use(iriszap.RecoveryWithZap(s.log, true))
	s.app.Use(iris.Compression)

	cfg := config.Global()

	// Register API routes
	apiParty := s.app.Party("/api")
	apiParty.Use(authMiddleware)
	apiParty.Post("/login", api.Login)
	apiParty.Post("/logout", api.Logout)
	apiParty.Post("/refresh", api.Refresh)
	api.RegisterProxyAPI(apiParty, s.proxyManager)

	// Register frontend routes
	s.registerFrontendRoutes()

	go func() {
		// 启动 HTTPS 服务
		err := s.app.Run(iris.TLS(":"+strconv.Itoa(cfg.HttpsPort), cfg.Cert.CertFile, cfg.Cert.KeyFile))
		if err != nil {
			zap.L().Panic("HTTPS server failed to start", zap.Error(err))
		}
	}()

	// 启动 HTTP 服务
	// 在这里loop，除非panic
	if err := s.app.Listen(":" + strconv.Itoa(cfg.HttpPort)); err != nil {
		zap.L().Panic("HTTP server failed to start", zap.Error(err))
	}

	return nil
}

func (s *Server) registerFrontendRoutes() {
	// Create a file system from the embedded filesystem.
	fileSystem := http.FS(s.frontendFS)

	routerOpt := router.DefaultDirOptions
	routerOpt.SPA = true
	s.app.HandleDir("/", fileSystem, routerOpt)
}

func authMiddleware(ctx iris.Context) {
	// Allow login path to bypass middleware
	if ctx.Path() == "/api/login" {
		ctx.Next()
		return
	}

	// Get the session token from the cookie
	token := ctx.GetCookie("session_id")
	if token == "" {
		ctx.StatusCode(iris.StatusUnauthorized)
		ctx.JSON(iris.Map{"message": "Unauthorized: Missing session cookie"})
		return
	}

	// Validate the session token
	if _, ok := session.Global().Get(token); !ok {
		ctx.StatusCode(iris.StatusUnauthorized)
		ctx.JSON(iris.Map{"message": "Unauthorized: Invalid or expired session"})
		return
	}

	ctx.Next()
}
