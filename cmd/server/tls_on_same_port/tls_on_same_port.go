package tls_on_same_port

import (
	"crypto/tls"
	"errors"
	"fmt"
	"fn_helper/internal/entity"
	"github.com/kataras/iris/v12"
	"go.uber.org/zap"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
	"net/http"
	"runtime"
	"strconv"
	"sync"
	"time"
)

func startHttpServerTlsOnSamePort(app *iris.Application, port int, cert entity.Cert) error {
	listenAddr := ":" + strconv.Itoa(port) // 服务器监听的端口
	numWorkers := runtime.NumCPU() * 2     // 嗅探器工作 Goroutine 数量，通常为 CPU 核心数的 1-4 倍

	// 创建自定义的嗅探监听器
	sniffL, err := newSniffListener(listenAddr, numWorkers)
	if err != nil {
		zap.S().Panic(err)
	}
	defer sniffL.Close() // 确保在 main 函数退出时关闭监听器

	// 来构建 Iris 应用，但不启动其内部监听器
	if err = runIrisAppWithoutServe(app); err != nil {
		zap.S().Panic(err)
	}

	var wg sync.WaitGroup // 用于等待两个服务器 Goroutine 结束

	// --- 1. 启动 HTTP 服务器 (支持 h2c) ---
	wg.Add(1)
	go func() {
		defer wg.Done()

		httpServer := &http.Server{
			Addr:           listenAddr,                           // 占位符，实际监听由 sniffListener 处理
			Handler:        h2c.NewHandler(app, &http2.Server{}), // 使用统一的 irisApp 作为处理器，并启用 h2c
			ReadTimeout:    10 * time.Second,                     // 读取请求头的超时时间
			WriteTimeout:   10 * time.Second,                     // 写入响应的超时时间
			IdleTimeout:    60 * time.Second,                     // 空闲连接的超时时间
			MaxHeaderBytes: 1 << 20,                              // 1MB
		}
		zap.L().Info("Starting HTTP server", zap.String("addr", listenAddr), zap.String("protocol", "h2c"))
		if err = httpServer.Serve(sniffL); err != nil && !errors.Is(err, http.ErrServerClosed) {
			zap.L().Panic("HTTP server failed to start", zap.Error(err))
		}
		zap.L().Info("HTTP server stopped.")
	}()

	// --- 2. 启动 HTTPS 服务器 (支持 HTTP/2) ---
	wg.Add(1)
	go func() {
		defer wg.Done()

		// 加载 TLS 证书和密钥
		tlsConfig := &tls.Config{
			Certificates: make([]tls.Certificate, 1),
			MinVersion:   tls.VersionTLS12, // 确保使用 TLS 1.2 或更高版本
			CurvePreferences: []tls.CurveID{
				tls.X25519,
				tls.CurveP256,
				tls.CurveP384,
			}, // 优先使用高性能椭圆曲线
			CipherSuites: []uint16{
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_RSA_WITH_AES_128_GCM_SHA256, // 如果需要支持 RSA 密钥交换
				tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
				// TLS 1.3 密码套件不需要显式列出，它们是默认支持的
			},
			PreferServerCipherSuites: true, // 服务器优先选择密码套件
		}
		tlsConfig.Certificates[0], err = tls.LoadX509KeyPair(cert.CertFile, cert.KeyFile)
		if err != nil {
			zap.L().Panic("Failed to load TLS certificates", zap.Error(err))
		}

		// 创建一个 TLS 监听器，其底层连接源自 sniffL.AcceptTLSWrapper()
		tlsListener := tls.NewListener(sniffL.AcceptTLSWrapper(), tlsConfig)

		httpsServer := &http.Server{
			Addr:           listenAddr, // 占位符
			Handler:        app,        // 使用统一的 irisApp 作为处理器
			ReadTimeout:    10 * time.Second,
			WriteTimeout:   10 * time.Second,
			IdleTimeout:    60 * time.Second,
			MaxHeaderBytes: 1 << 20,
		}
		// 配置 HTTPS 服务器以支持 HTTP/2 (通过 ALPN 协商)
		err = http2.ConfigureServer(httpsServer, &http2.Server{})
		if err != nil {
			zap.S().Panic(err)
		}

		zap.L().Info("Starting HTTPS server", zap.String("addr", listenAddr), zap.String("protocol", "HTTP/2"))
		if err = httpsServer.Serve(tlsListener); err != nil && !errors.Is(err, http.ErrServerClosed) {
			zap.L().Panic("HTTPS server failed to start", zap.Error(err))
		}
		zap.L().Info("HTTPS server stopped.")
	}()

	// 等待所有服务器 Goroutine 结束
	wg.Wait()
	zap.L().Info("All sniffed servers stopped.")
	return err
}

// 封装了 Iris 应用的构建和主机配置，但不监听端口
func runIrisAppWithoutServe(app *iris.Application, withOrWithout ...iris.Configurator) error {
	app.Configure(withOrWithout...)

	if err := app.Build(); err != nil {
		return err
	}

	app.ConfigureHost(func(host *iris.Supervisor) {
		host.SocketSharding = app.ConfigurationReadOnly().GetSocketSharding()
		host.KeepAlive = app.ConfigurationReadOnly().GetKeepAlive()
	})

	if len(app.Hosts) > 0 {
		zap.L().Debug(fmt.Sprintf("Application: running using %d host(s)", len(app.Hosts)+1 /* +1 the current */))
	}

	return nil
}
