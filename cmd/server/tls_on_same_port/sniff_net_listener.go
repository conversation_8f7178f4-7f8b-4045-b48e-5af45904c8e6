package tls_on_same_port

import (
	"bufio"
	"go.uber.org/zap"
	"io"
	"net"
	"strings"
	"sync"
	"time"
)

const (
	TLSHandshakeByte = 0x16
)

// firstByteConnPool 用于复用 firstByteConn 实例
var firstByteConnPool = sync.Pool{
	New: func() interface{} {
		return new(firstByteConn)
	},
}

// bufioReaderPool 用于复用 bufio.Reader 实例
var bufioReaderPool = sync.Pool{
	New: func() interface{} {
		return bufio.NewReader(nil) // 初始 reader 为 nil
	},
}

// sniffListener 是一个自定义的 net.Listener，它通过嗅探第一个字节
// 来判断连接是 TLS 还是 HTTP。
type sniffListener struct {
	net.Listener
	httpCh  chan net.Conn // 用于普通 HTTP 连接的通道
	tlsCh   chan net.Conn // 用于 TLS 连接的通道
	connCh  chan net.Conn // 用于接收原始连接的通道，实现工作 Goroutine 池
	wg      sync.WaitGroup
	quit    chan struct{}
	workers int // 工作 Goroutine 数量
}

// newSniffListener 创建一个新的 sniffListener 实例，并启动工作 Goroutine 池。
func newSniffListener(listenAddr string, numWorkers int) (*sniffListener, error) {
	stdListener, err := net.Listen("tcp", listenAddr)
	if err != nil {
		return nil, err
	}

	sl := &sniffListener{
		Listener: stdListener,
		httpCh:   make(chan net.Conn, 2048), // 增大缓冲，适应高并发
		tlsCh:    make(chan net.Conn, 2048), // 增大缓冲，适应高并发
		connCh:   make(chan net.Conn, 4096), // 原始连接缓冲，应大于 httpCh/tlsCh
		quit:     make(chan struct{}),
		workers:  numWorkers,
	}

	// 启动固定数量的工作 Goroutine，从 connCh 获取连接并处理
	for i := 0; i < sl.workers; i++ {
		sl.wg.Add(1)
		go sl.worker()
	}

	// 启动专门的 Goroutine 负责接受新连接
	sl.wg.Add(1)
	go sl.acceptLoop()

	return sl, nil
}

// acceptLoop 专门负责从底层监听器接受连接，并将其发送到 connCh。
func (sl *sniffListener) acceptLoop() {
	defer sl.wg.Done()
	for {
		select {
		case <-sl.quit: // 收到退出信号，停止接受新连接
			return
		default:
			conn, err := sl.Listener.Accept()
			if err != nil {
				if opErr, ok := err.(*net.OpError); ok && opErr.Timeout() {
					// accept timeout, can be ignored or handled if Listener has SetAcceptTimeout
				} else if strings.Contains(err.Error(), "use of closed network connection") {
					zap.L().Info("sniffListener: underlying listener closed, acceptLoop exiting.")
					return
				}
				zap.L().Error("sniffListener: accept error", zap.Error(err))
				time.Sleep(100 * time.Millisecond) // 避免在错误时忙循环
				continue
			}

			// 将接受到的连接发送给工作 Goroutine 池
			select {
			case sl.connCh <- conn:
				// 连接已排队
			case <-sl.quit:
				conn.Close() // 服务器正在关闭，关闭新接受的连接
				return
			default:
				zap.L().Warn("sniffListener: connCh is full, dropping connection.", zap.String("remote_addr", conn.RemoteAddr().String()))
				conn.Close() // 通道已满，丢弃此连接
			}
		}
	}
}

// worker Goroutine 从 connCh 获取连接并执行嗅探和分发逻辑。
func (sl *sniffListener) worker() {
	defer sl.wg.Done()
	for {
		select {
		case <-sl.quit: // 收到退出信号，工作 Goroutine 退出
			return
		case conn, ok := <-sl.connCh:
			if !ok { // connCh 已关闭，所有连接都已处理完毕或通道已耗尽
				return
			}
			sl.sniffAndRoute(conn) // 调用嗅探和分发逻辑
		}
	}
}

// Close 关闭底层监听器并通知所有相关 Goroutine 停止。
func (sl *sniffListener) Close() error {
	close(sl.quit)             // 首先发送退出信号
	err := sl.Listener.Close() // 关闭底层监听器，这将使 acceptLoop 的 Accept() 返回错误并退出

	sl.wg.Wait()     // 等待所有 Goroutine (acceptLoop 和 workers) 结束
	close(sl.connCh) // 关闭原始连接通道
	close(sl.httpCh) // 关闭 HTTP 连接分发通道
	close(sl.tlsCh)  // 关闭 TLS 连接分发通道
	return err
}

// Accept 方法实现了 net.Listener 接口，供 HTTP 服务器使用。
// 它从 httpCh 通道中获取连接。
func (sl *sniffListener) Accept() (net.Conn, error) {
	conn, ok := <-sl.httpCh
	if !ok { // 通道已关闭
		return nil, net.ErrClosed
	}
	return conn, nil
}

// AcceptTLS 方法提供给 TLS 服务器使用，以接收 TLS 连接。
// 它从 tlsCh 通道中获取连接。
func (sl *sniffListener) AcceptTLS() (net.Conn, error) {
	conn, ok := <-sl.tlsCh
	if !ok { // 通道已关闭
		return nil, net.ErrClosed
	}
	return conn, nil
}

// sniffAndRoute 负责嗅探连接的第一个字节并将其分发到正确的通道。
func (sl *sniffListener) sniffAndRoute(conn net.Conn) {
	// 设置读取超时，防止客户端不发送任何数据而导致 Goroutine 阻塞
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	b := make([]byte, 1)
	n, err := conn.Read(b)

	if err != nil {
		if err != io.EOF {
			zap.L().Warn("sniffAndRoute: failed to read first byte", zap.String("remote_addr", conn.RemoteAddr().String()), zap.Error(err))
		}
		conn.Close() // 关闭此连接
		return       // 立即返回，不再尝试发送连接
	}
	if n == 0 { // 没有读取到任何字节
		zap.L().Warn("sniffAndRoute: no bytes read, closing connection.", zap.String("remote_addr", conn.RemoteAddr().String()))
		conn.Close()
		return // 立即返回
	}

	conn.SetReadDeadline(time.Time{}) // 重置读取超时

	// --- 使用 sync.Pool 优化 firstByteConn 和 bufio.Reader ---
	wrappedConn := firstByteConnPool.Get().(*firstByteConn)
	reader := bufioReaderPool.Get().(*bufio.Reader)
	reader.Reset(conn) // 重置 reader，使其从当前连接开始读取

	// 初始化 wrappedConn 的状态
	wrappedConn.conn = conn
	wrappedConn.firstByte = b[0]
	wrappedConn.hasRead = false // 重置状态
	wrappedConn.bufReader = reader
	// ----------------------------------------------------

	// 检查是否是 TLS 握手字节
	if b[0] == TLSHandshakeByte {
		select {
		case sl.tlsCh <- wrappedConn:
			// 连接已发送
		case <-sl.quit:
			wrappedConn.Close() // 服务器正在关闭，关闭连接
		default:
			zap.L().Warn("sniffListener: TLS channel is full, closing connection.", zap.String("remote_addr", conn.RemoteAddr().String()))
			wrappedConn.Close()
		}
	} else {
		select {
		case sl.httpCh <- wrappedConn:
			// 连接已发送
		case <-sl.quit:
			wrappedConn.Close() // 服务器正在关闭，关闭连接
		default:
			zap.L().Warn("sniffListener: HTTP channel is full, closing connection.", zap.String("remote_addr", conn.RemoteAddr().String()))
			wrappedConn.Close()
		}
	}
}

// firstByteConn 是一个 net.Conn 的包装器，优化后使用 sync.Pool 管理。
type firstByteConn struct {
	conn      net.Conn
	firstByte byte          // 在嗅探过程中读取的单个字节
	hasRead   bool          // 标记是否已经提供了 firstByte
	bufReader *bufio.Reader // 从池中获取并重置
}

func (fbc *firstByteConn) Read(b []byte) (n int, err error) {
	if !fbc.hasRead {
		if len(b) > 0 {
			b[0] = fbc.firstByte
			fbc.hasRead = true
			n = 1
			if len(b) > 1 && fbc.bufReader != nil {
				n2, err := fbc.bufReader.Read(b[1:])
				return n + n2, err
			}
			return n, nil
		}
		return 0, nil
	}
	if fbc.bufReader != nil {
		return fbc.bufReader.Read(b)
	}
	// 作为最后的备用，直接从底层连接读取（不应该发生，因为 bufReader 总是初始化）
	return fbc.conn.Read(b)
}

func (fbc *firstByteConn) Write(b []byte) (n int, err error) {
	return fbc.conn.Write(b)
}

// Close 方法在连接关闭时，将 firstByteConn 和 bufio.Reader 返回到池中。
func (fbc *firstByteConn) Close() error {
	err := fbc.conn.Close() // 先关闭底层连接

	// 重置 firstByteConn 的状态，准备放入池中
	fbc.conn = nil      // 清空对底层连接的引用
	fbc.hasRead = false // 重置hasRead状态

	// 将 bufio.Reader 放回池中
	if fbc.bufReader != nil {
		fbc.bufReader.Reset(nil) // 重置 reader 的底层 io.Reader
		bufioReaderPool.Put(fbc.bufReader)
		fbc.bufReader = nil // 清空引用
	}

	// 将 firstByteConn 实例放回池中
	firstByteConnPool.Put(fbc)

	return err
}

func (fbc *firstByteConn) LocalAddr() net.Addr {
	return fbc.conn.LocalAddr()
}

func (fbc *firstByteConn) RemoteAddr() net.Addr {
	return fbc.conn.RemoteAddr()
}

func (fbc *firstByteConn) SetDeadline(t time.Time) error {
	return fbc.conn.SetDeadline(t)
}

func (fbc *firstByteConn) SetReadDeadline(t time.Time) error {
	return fbc.conn.SetReadDeadline(t)
}

func (fbc *firstByteConn) SetWriteDeadline(t time.Time) error {
	return fbc.conn.SetWriteDeadline(t)
}

// AcceptTLSWrapper 是一个辅助类型，它实现了 net.Listener 接口，
// 用于 tls.NewListener 从 sniffListener.tlsCh 消费连接。
type AcceptTLSWrapper struct {
	sl *sniffListener
}

func (sl *sniffListener) AcceptTLSWrapper() *AcceptTLSWrapper {
	return &AcceptTLSWrapper{sl: sl}
}

func (atw *AcceptTLSWrapper) Accept() (net.Conn, error) {
	return atw.sl.AcceptTLS()
}

func (atw *AcceptTLSWrapper) Close() error {
	return nil // 关闭 sniffListener 本身会关闭底层监听器
}

func (atw *AcceptTLSWrapper) Addr() net.Addr {
	return atw.sl.Addr()
}
