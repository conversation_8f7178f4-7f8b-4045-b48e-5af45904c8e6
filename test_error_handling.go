package main

import (
	"fmt"
	"net/http"
)

// 模拟错误处理器
func mockErrorHandler(rw http.ResponseWriter, req *http.Request, err error) {
	fmt.Printf("处理代理错误: %v\n", err)
	fmt.Printf("请求方法: %s\n", req.Method)
	fmt.Printf("请求URL: %s\n", req.URL.String())
	
	// 检查是否是客户端断开连接的错误
	if err.Error() == "net/http: abort Handler" {
		fmt.Println("客户端断开连接，不写响应")
		return
	}
	
	// 其他错误返回502 Bad Gateway
	fmt.Println("返回502 Bad Gateway")
	rw.WriteHeader(http.StatusBadGateway)
	rw.Write([]byte("代理服务器错误"))
}

func main() {
	fmt.Println("测试错误处理器:")
	fmt.Println("================")
	
	// 模拟不同类型的错误
	testErrors := []error{
		fmt.Errorf("net/http: abort Handler"),
		fmt.Errorf("connection refused"),
		fmt.Errorf("timeout"),
		fmt.Errorf("unknown error"),
	}
	
	// 创建模拟请求
	req, _ := http.NewRequest("GET", "http://localhost:8080/home/<USER>/style.css", nil)
	
	for i, err := range testErrors {
		fmt.Printf("\n测试 %d: %v\n", i+1, err)
		fmt.Println("---")
		
		// 模拟ResponseWriter（这里只是打印，实际中会写入响应）
		mockErrorHandler(nil, req, err)
	}
}
