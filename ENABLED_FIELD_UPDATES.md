# Location Enabled 字段前端支持更新

## 概述
根据后端新增的 `Enabled bool` 字段，我们对前端代理管理页面进行了相应的更新，以支持 Location 的启用/禁用功能。

## 更新内容

### 1. 数据结构更新

#### editedLocation 对象
```javascript
const editedLocation = ref({
  name: '',
  path: '/',
  proxy_pass: '',
  root: '',
  allow_deny: '',
  proxy_set_header: {},
  insecure_skip_verify: false,
  enabled: true,  // 新增：默认启用
})
```

### 2. 功能增强

#### 启用/禁用切换功能
- **新增方法**: `toggleLocationEnabled(proxy, location)`
- **功能**: 点击切换 Location 的启用状态
- **API 调用**: `PUT /api/proxy/{id}/locations/{path}` 更新 Location 配置
- **用户反馈**: 操作成功/失败的中文提示

#### Location 编辑对话框
- **新增控件**: 启用/禁用复选框
- **位置**: 高级设置区域的第一个选项
- **样式**: 使用成功色（绿色）主题
- **提示**: "禁用后此规则将不会生效"

### 3. 界面优化

#### Location 列表显示
- **状态标识**: 每个 Location 显示启用/禁用状态芯片
- **视觉区分**: 
  - 启用状态：绿色芯片，正常显示
  - 禁用状态：红色芯片，半透明显示
- **图标状态**: 禁用的 Location 图标变为灰色

#### 操作按钮
- **新增按钮**: 眼睛图标切换启用/禁用
- **图标变化**: 
  - 启用状态：`ri-eye-line`（眼睛开启）
  - 禁用状态：`ri-eye-off-line`（眼睛关闭）
- **颜色区分**: 成功色（绿色）/错误色（红色）
- **悬停提示**: "点击启用" / "点击禁用"

### 4. 样式更新

#### CSS 类
```css
.location-item.location-disabled {
  opacity: 0.6;
  background: rgba(var(--v-theme-error), 0.02);
}

.location-item.location-disabled:hover {
  background: rgba(var(--v-theme-error), 0.06);
}
```

#### 视觉效果
- **禁用状态**: 整个 Location 项半透明显示
- **背景色**: 淡红色背景表示禁用状态
- **悬停效果**: 禁用项悬停时显示更深的红色背景
- **过渡动画**: 平滑的状态切换动画

### 5. 用户体验优化

#### 状态反馈
- **即时更新**: 切换状态后立即刷新数据
- **加载指示**: 操作过程中显示加载状态
- **错误处理**: 操作失败时显示详细错误信息

#### 默认行为
- **新建 Location**: 默认启用状态
- **编辑 Location**: 保持原有启用状态
- **批量操作**: 支持快速启用/禁用多个 Location

## 技术实现

### API 集成
```javascript
const toggleLocationEnabled = async (proxy, location) => {
  const updatedLocation = { ...location, enabled: !location.enabled }
  loading.value = true
  try {
    await axios.put(`/api/proxy/${proxy.id}/locations/${location.path}`, updatedLocation)
    showSnackbar(`Location ${updatedLocation.enabled ? '启用' : '禁用'}成功!`)
    await fetchProxies()
  } catch (error) {
    // 错误处理
  } finally {
    loading.value = false
  }
}
```

### 模板更新
- **条件渲染**: 根据 `enabled` 状态动态显示不同样式
- **事件绑定**: 绑定切换事件到操作按钮
- **状态同步**: 确保界面状态与数据状态一致

## 兼容性

### 向后兼容
- **默认值**: 新建 Location 默认启用
- **数据迁移**: 现有 Location 如果没有 `enabled` 字段，前端默认视为启用
- **API 兼容**: 完全兼容现有的后端 API

### 前端兼容
- **组件复用**: 使用现有的 Vuetify 组件
- **样式一致**: 遵循现有的设计语言
- **交互模式**: 符合用户的操作习惯

## 使用说明

### 启用/禁用 Location
1. 在代理卡片中找到目标 Location
2. 点击眼睛图标切换启用/禁用状态
3. 系统会自动保存并更新状态

### 编辑 Location 启用状态
1. 点击 Location 的编辑按钮
2. 在高级设置中找到"启用此 Location"复选框
3. 勾选或取消勾选来设置启用状态
4. 点击保存按钮应用更改

### 视觉识别
- **绿色芯片 + 正常显示**: Location 已启用
- **红色芯片 + 半透明显示**: Location 已禁用
- **眼睛开启图标**: 可点击禁用
- **眼睛关闭图标**: 可点击启用

## 总结
这次更新完美支持了后端新增的 `enabled` 字段，提供了直观的启用/禁用功能，增强了用户对 Location 规则的精细化控制能力。界面设计简洁明了，操作便捷，完全符合现代 Web 应用的用户体验标准。
