package main

import (
	"fmt"
	"log"

	"fn_helper/internal/feature/config"
	"fn_helper/internal/feature/proxy"
)

func main() {
	// 加载配置
	if err := config.Load(""); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	cfg := config.Global()
	fmt.Printf("当前配置中的代理数量: %d\n", len(cfg.Proxy))
	
	for i, p := range cfg.Proxy {
		fmt.Printf("代理 %d: ID=%s, Name=%s, Port=%d, Enabled=%t, Locations=%d\n", 
			i+1, p.Id, p.Name, p.ListenPort, p.Enabled, len(p.Locations))
	}

	// 创建一个测试代理
	testProxy := config.ProxyRule{
		Name:       "测试代理",
		Enabled:    true,
		ListenPort: 9999,
		ListenHost: "",
		Locations:  []config.ProxyLocation{},
	}

	// 使用NewWebProxy创建代理（这会强制生成新ID）
	webProxy := proxy.NewWebProxy(testProxy)
	fmt.Printf("新创建的代理ID: %s\n", webProxy.GetId())

	// 添加到配置中
	cfg.Proxy = append(cfg.Proxy, webProxy.GetRule())
	
	// 写入配置文件
	if err := cfg.WriteToDisk(); err != nil {
		log.Fatalf("写入配置失败: %v", err)
	}
	
	fmt.Println("配置已写入磁盘")

	// 重新加载配置验证
	if err := config.Load(""); err != nil {
		log.Fatalf("重新加载配置失败: %v", err)
	}

	newCfg := config.Global()
	fmt.Printf("重新加载后的代理数量: %d\n", len(newCfg.Proxy))
	
	for i, p := range newCfg.Proxy {
		fmt.Printf("代理 %d: ID=%s, Name=%s, Port=%d, Enabled=%t, Locations=%d\n", 
			i+1, p.Id, p.Name, p.ListenPort, p.Enabled, len(p.Locations))
	}
}
