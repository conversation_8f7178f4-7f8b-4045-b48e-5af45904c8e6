package main

import (
	"fmt"
	"strings"
)

// 复制相关函数进行测试
func shouldSkipPath(path string) bool {
	if path == "" {
		return true
	}
	if strings.Contains(path, "://") {
		return true
	}
	if strings.HasPrefix(path, "//") {
		return true
	}
	if strings.HasPrefix(path, "data:") {
		return true
	}
	if strings.HasPrefix(path, "#") {
		return true
	}
	if strings.HasPrefix(path, "mailto:") || strings.HasPrefix(path, "tel:") || 
	   strings.HasPrefix(path, "javascript:") || strings.HasPrefix(path, "blob:") {
		return true
	}
	return false
}

func isStaticResource(path string) bool {
	staticExtensions := []string{
		".js", ".css", ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico",
		".woff", ".woff2", ".ttf", ".eot", ".otf", ".mp4", ".webm", ".mp3",
		".pdf", ".zip", ".json", ".xml", ".txt", ".map",
	}
	
	lowerPath := strings.ToLower(path)
	
	for _, ext := range staticExtensions {
		if strings.HasSuffix(lowerPath, ext) {
			return true
		}
	}
	
	return false
}

func looksLikeAsset(path string) bool {
	if strings.Contains(path, "?") || strings.Contains(path, "=") {
		return false
	}
	
	hashPatterns := []string{
		"-", "_", ".",
	}
	
	lowerPath := strings.ToLower(path)
	
	for _, pattern := range hashPatterns {
		if strings.Contains(path, pattern) {
			if strings.HasSuffix(lowerPath, ".js") || 
			   strings.HasSuffix(lowerPath, ".css") {
				return true
			}
		}
	}
	
	return false
}

// rewriteSinglePathWithMode 重写单个路径，统一使用相对路径格式
func rewriteSinglePathWithMode(path, pathPrefix string, useRelativePath bool) string {
	// 跳过不需要重写的路径
	if shouldSkipPath(path) {
		return path
	}

	// 处理绝对路径
	if strings.HasPrefix(path, "/") && !strings.HasPrefix(path, "//") {
		// 检查是否已经有代理前缀
		if !strings.HasPrefix(path, pathPrefix+"/") && path != pathPrefix {
			// 统一使用相对路径格式，去掉前导斜杠
			return strings.TrimPrefix(pathPrefix, "/") + path
		}
	} else if !strings.Contains(path, "://") && !strings.HasPrefix(path, "#") {
		// 处理相对路径
		cleanPath := path

		// 处理 ./ 和 ../ 前缀
		if strings.HasPrefix(path, "./") {
			cleanPath = strings.TrimPrefix(path, "./")
		}

		if strings.HasPrefix(path, "../") {
			cleanPath = strings.TrimPrefix(path, "../")
		}

		if isStaticResource(cleanPath) || looksLikeAsset(cleanPath) {
			// 统一使用相对路径格式，去掉前导斜杠
			return strings.TrimPrefix(pathPrefix, "/") + "/" + cleanPath
		}
	}

	return path
}

// rewriteSinglePath 重写单个路径，统一使用相对路径格式
func rewriteSinglePath(path, pathPrefix string) string {
	return rewriteSinglePathWithMode(path, pathPrefix, false)
}

func main() {
	pathPrefix := "/home"
	
	// 测试各种路径
	testPaths := []string{
		"assets/logo-q9jT1N0.js",
		"./main-abc123.css", 
		"../utils.js",
		"./error__DJJHekw7.js",
		"Vting-D9EVcE-h.js",
		"/assets/style.css",
		"hello world",
		"https://example.com/lib.js",
		"#anchor",
		"mailto:<EMAIL>",
	}
	
	fmt.Println("统一相对路径重写测试:")
	fmt.Println("============================================================")
	
	for _, path := range testPaths {
		newPath := rewriteSinglePath(path, pathPrefix)
		changed := newPath != path
		
		fmt.Printf("原路径: %-30s | 新路径: %-35s | 已改变: %t\n", 
			path, newPath, changed)
	}
	
	fmt.Println("\n预期的浏览器解析结果 (假设当前页面是 http://localhost:8080/home/<USER>")
	fmt.Println("============================================================")
	
	baseURL := "http://localhost:8080/home/"
	for _, path := range testPaths {
		newPath := rewriteSinglePath(path, pathPrefix)
		if newPath != path && !shouldSkipPath(newPath) {
			fmt.Printf("重写路径: %-35s | 浏览器解析: %s%s\n", 
				newPath, baseURL, newPath)
		}
	}
}
