export const routes = [
  { path: '/', redirect: '/dashboard' },
  {
    path: '/',
    component: () => import('@/layouts/default.vue'),
    children: [
      {
        path: 'dashboard',
        component: () => import('@/pages/dashboard.vue'),
      },
      {
        path: 'settings',
        component: () => import('@/pages/settings.vue'),
      },
      {
        path: 'proxy',
        component: () => import('@/pages/proxy.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/layouts/blank.vue'),
    children: [
      {
        path: '',
        name: 'login',
        component: () => import('@/pages/login.vue'),
      },
    ],
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/pages/[...error].vue'),
  },
]
