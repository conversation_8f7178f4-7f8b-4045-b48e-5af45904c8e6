import { createRouter, createWebHistory } from 'vue-router'
import { routes } from './routes'
import { setupAxiosInterceptors } from '../axios'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

export default function (app) {
  app.use(router)

  // Setup axios interceptors inside the plugin setup function
  // to ensure Pinia is already initialized.
  setupAxiosInterceptors(router)

  router.beforeEach((to, from, next) => {
    const auth = JSON.parse(localStorage.getItem('auth') || 'false')

    if (auth && to.path === '/login') {
      // If user is logged in and tries to access login page, redirect to home
      next({ path: '/' })
    } else if (!auth && to.path !== '/login') {
      // If user is not logged in and tries to access a protected page, redirect to login
      next({ path: '/login' })
    } else {
      // Otherwise, allow navigation
      next()
    }
  })
}
export { router }
