import axios from 'axios'
import { useAuthStore } from '@/store/auth'

// Create a dedicated axios instance for the app
const apiClient = axios.create({
  // You can add baseURL or other default settings here
})

// Setup response interceptors. This is exported so router plugin can call it
export const setupAxiosInterceptors = router => {
  apiClient.interceptors.response.use(
    response => response,
    error => {
      if (error.response && error.response.status === 401) {
        const authStore = useAuthStore()
        // Clear stale auth state
        authStore.logout()
        // Redirect to login if we are not already there
        if (router.currentRoute.value.path !== '/login') {
          router.push('/login')
        }
      }
      return Promise.reject(error)
    },
  )
}

// ---------------------------------------------
// Vue plugin installation function
// ---------------------------------------------
// We export a proper install function as default so the
// auto-plugin loader will call it with the Vue app instance.
// This prevents the app object from being passed to the axios
// instance (which previously caused an infinite deep-merge loop
// inside axios and triggered the `Maximum call stack size exceeded` error).
// Vue 插件安装函数
function installAxios(app) {
  // Provide axios instance globally via this.$axios
  app.config.globalProperties.$axios = apiClient
}

// 将 axios 实例的方法和属性合并到安装函数上，
// 这样默认导出既是插件函数，也可以作为 axios 实例直接使用
Object.assign(installAxios, apiClient)

// 默认导出合并后的插件/实例
export default installAxios

// Optional named export in case components want to import it directly
export { apiClient }
