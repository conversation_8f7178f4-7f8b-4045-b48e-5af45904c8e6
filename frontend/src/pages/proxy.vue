<script setup>
import { ref, onMounted, computed } from 'vue'
import axios from '@/plugins/axios'
import ProxyStatusIndicator from '@/components/ProxyStatusIndicator.vue'

// -- Data --
const proxies = ref([])
const dialog = ref(false)
const locationDialog = ref(false)
const isEditMode = ref(false)
const selectedProxy = ref(null)
const editedProxy = ref({
  id: '',
  name: '',
  listen_port: null,
  listen_host: '',
  ssl_certificate: '',
  ssl_certificate_key: '',
  locations: [],
})
const editedLocation = ref({
  name: '',
  path: '/',
  proxy_pass: '',
  root: '',
  allow_deny: '',
  proxy_set_header: {},
  insecure_skip_verify: false,
  enabled: true,
})
const isLocationEditMode = ref(false)
const selectedLocationIndex = ref(-1)
const snackbar = ref(false)
const snackbarText = ref('')
const snackbarColor = ref('success')
const loading = ref(false)
const search = ref('')

// -- Computed --
const filteredProxies = computed(() => {
  if (!search.value) return proxies.value
  return proxies.value.filter(proxy =>
    proxy.name?.toLowerCase().includes(search.value.toLowerCase()) ||
    proxy.id?.toLowerCase().includes(search.value.toLowerCase()) ||
    proxy.listen_port?.toString().includes(search.value)
  )
})

// -- Validation Rules --
const rules = {
  required: value => !!value || '此字段为必填项',
  port: value => (value > 0 && value <= 65535) || '端口必须在 1-65535 之间',
  url: value => !value || /^https?:\/\/.+/.test(value) || '请输入有效的 URL (http:// 或 https://)',
  path: value => !value || value.startsWith('/') || '路径必须以 / 开头',
}

// -- Methods --

const fetchProxies = async () => {
  loading.value = true
  try {
    const { data } = await axios.get('/api/proxy')
    proxies.value = data || []
  } catch (error) {
    if (error.response?.status !== 401) {
      showSnackbar(`获取代理列表失败: ${error.response?.data?.message || error.message}`, 'error')
    }
  } finally {
    loading.value = false
  }
}

const openAddDialog = () => {
  isEditMode.value = false
  editedProxy.value = {
    id: '',
    name: '',
    enabled: true,
    listen_port: 8080,
    listen_host: '',
    ssl_certificate: '',
    ssl_certificate_key: '',
    locations: [],
  }
  dialog.value = true
}

const openEditDialog = proxy => {
  isEditMode.value = true
  editedProxy.value = JSON.parse(JSON.stringify(proxy))
  dialog.value = true
}

const openLocationDialog = (proxy, locationIndex = -1) => {
  selectedProxy.value = proxy
  selectedLocationIndex.value = locationIndex
  isLocationEditMode.value = locationIndex >= 0

  if (isLocationEditMode.value) {
    editedLocation.value = JSON.parse(JSON.stringify(proxy.locations[locationIndex]))
  } else {
    editedLocation.value = {
      name: '',
      path: '/',
      proxy_pass: '',
      root: '',
      allow_deny: '',
      proxy_set_header: {},
      insecure_skip_verify: false,
      enabled: true,
    }
  }
  locationDialog.value = true
}

const saveProxy = async () => {
  if (!editedProxy.value.listen_port) {
    showSnackbar('请填写必填字段', 'error')
    return
  }

  loading.value = true
  try {
    if (isEditMode.value) {
      await axios.put(`/api/proxy/${editedProxy.value.id}`, editedProxy.value)
      showSnackbar('代理更新成功!')
    } else {
      const response = await axios.post('/api/proxy', editedProxy.value)
      showSnackbar('代理创建成功!')
    }
    closeDialog()
    await fetchProxies()
  } catch (error) {
    if (error.response?.status !== 401) {
      showSnackbar(`保存代理失败: ${error.response?.data?.message || error.message}`, 'error')
    }
  } finally {
    loading.value = false
  }
}

const saveLocation = async () => {
  if (!editedLocation.value.path || (!editedLocation.value.proxy_pass && !editedLocation.value.root)) {
    showSnackbar('请填写路径和目标地址或根目录', 'error')
    return
  }

  loading.value = true
  try {
    if (isLocationEditMode.value) {
      await axios.put(`/api/proxy/${selectedProxy.value.id}/locations/${editedLocation.value.path}`, editedLocation.value)
      showSnackbar('Location 更新成功!')
    } else {
      await axios.post(`/api/proxy/${selectedProxy.value.id}/locations`, editedLocation.value)
      showSnackbar('Location 添加成功!')
    }
    closeLocationDialog()
    await fetchProxies()
  } catch (error) {
    if (error.response?.status !== 401) {
      showSnackbar(`保存 Location 失败: ${error.response?.data?.message || error.message}`, 'error')
    }
  } finally {
    loading.value = false
  }
}

const deleteProxy = async proxy => {
  if (confirm(`确定要删除代理 "${proxy.name || proxy.id}" 吗？`)) {
    loading.value = true
    try {
      await axios.delete(`/api/proxy/${proxy.id}`)
      showSnackbar('代理删除成功!')
      await fetchProxies()
    } catch (error) {
      if (error.response?.status !== 401) {
        showSnackbar(`删除代理失败: ${error.response?.data?.message || error.message}`, 'error')
      }
    } finally {
      loading.value = false
    }
  }
}

const deleteLocation = async (proxy, location) => {
  if (confirm(`确定要删除 Location "${location.path}" 吗？`)) {
    loading.value = true
    try {
      await axios.delete(`/api/proxy/${proxy.id}/locations/${location.path}`)
      showSnackbar('Location 删除成功!')
      await fetchProxies()
    } catch (error) {
      if (error.response?.status !== 401) {
        showSnackbar(`删除 Location 失败: ${error.response?.data?.message || error.message}`, 'error')
      }
    } finally {
      loading.value = false
    }
  }
}

const toggleProxyStatus = async proxy => {
  // 基于当前运行状态决定操作：如果正在运行则停止（enabled=false），否则启动（enabled=true）
  const shouldStart = !proxy.running
  const updatedProxy = { ...proxy, enabled: shouldStart }
  loading.value = true
  try {
    await axios.put(`/api/proxy/${proxy.id}`, updatedProxy)
    showSnackbar(`代理${shouldStart ? '启动' : '停止'}成功!`)
    await fetchProxies()
  } catch (error) {
    if (error.response?.status !== 401) {
      showSnackbar(`${shouldStart ? '启动' : '停止'}代理失败: ${error.response?.data?.message || error.message}`, 'error')
    }
  } finally {
    loading.value = false
  }
}

const toggleLocationEnabled = async (proxy, location) => {
  const updatedLocation = { ...location, enabled: !location.enabled }
  loading.value = true
  try {
    await axios.put(`/api/proxy/${proxy.id}/locations/${location.path}`, updatedLocation)
    showSnackbar(`Location ${updatedLocation.enabled ? '启用' : '禁用'}成功!`)
    await fetchProxies()
  } catch (error) {
    if (error.response?.status !== 401) {
      showSnackbar(`${updatedLocation.enabled ? '启用' : '禁用'} Location 失败: ${error.response?.data?.message || error.message}`, 'error')
    }
  } finally {
    loading.value = false
  }
}

const moveLocation = async (proxy, fromIndex, toIndex) => {
  if (fromIndex === toIndex) return

  const locations = [...proxy.locations]
  const [movedLocation] = locations.splice(fromIndex, 1)
  locations.splice(toIndex, 0, movedLocation)

  const updatedProxy = { ...proxy, locations }

  loading.value = true
  try {
    await axios.put(`/api/proxy/${proxy.id}`, updatedProxy)
    showSnackbar('Location 顺序更新成功!')
    await fetchProxies()
  } catch (error) {
    if (error.response?.status !== 401) {
      showSnackbar(`更新 Location 顺序失败: ${error.response?.data?.message || error.message}`, 'error')
    }
  } finally {
    loading.value = false
  }
}

const addHeaderField = () => {
  if (!editedLocation.value.proxy_set_header) {
    editedLocation.value.proxy_set_header = {}
  }
  editedLocation.value.proxy_set_header[''] = ''
}

const removeHeaderField = (key) => {
  delete editedLocation.value.proxy_set_header[key]
}

const closeDialog = () => {
  dialog.value = false
}

const closeLocationDialog = () => {
  locationDialog.value = false
}

const showSnackbar = (text, color = 'success') => {
  snackbarText.value = text
  snackbarColor.value = color
  snackbar.value = true
}

const getStatusColor = (isRunning) => {
  return isRunning ? 'success' : 'error'
}

const getStatusText = (isRunning) => {
  return isRunning ? '运行中' : '已停止'
}

// -- Lifecycle Hooks --
onMounted(() => {
  fetchProxies()
})
</script>

<template>
  <VContainer fluid class="proxy-management">
    <!-- Main Card -->
    <VCard>
      <!-- Header -->
      <VCardTitle class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <VIcon icon="ri-server-line" class="me-3" size="24" />
          <span class="text-h5">代理管理</span>
        </div>
        <VBtn color="primary" prepend-icon="ri-add-line" @click="openAddDialog">
          新建代理
        </VBtn>
      </VCardTitle>

      <VCardText>
        <!-- Search -->
        <VTextField
          v-model="search"
          prepend-inner-icon="ri-search-line"
          label="搜索代理..."
          variant="outlined"
          density="compact"
          clearable
          class="mb-6"
        />

        <!-- Proxy Cards -->
        <VRow v-if="!loading && filteredProxies.length > 0">
      <VCol v-for="proxy in filteredProxies" :key="proxy.id" cols="12" md="6" lg="4">
        <VCard class="proxy-card" elevation="2">
          <VCardTitle class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <ProxyStatusIndicator
                :is-running="proxy.running"
                :loading="loading && selectedProxy?.id === proxy.id"
                class="me-3"
              />
              <span class="text-h6">{{ proxy.name || proxy.id }}</span>
            </div>

            <VMenu>
              <template #activator="{ props }">
                <VBtn icon="ri-more-2-line" size="small" variant="text" v-bind="props" />
              </template>
              <VList>
                <VListItem @click="openEditDialog(proxy)">
                  <template #prepend>
                    <VIcon icon="ri-pencil-line" />
                  </template>
                  <VListItemTitle>编辑</VListItemTitle>
                </VListItem>
                <VListItem @click="toggleProxyStatus(proxy)">
                  <template #prepend>
                    <VIcon :icon="proxy.running ? 'ri-stop-circle-line' : 'ri-play-circle-line'" />
                  </template>
                  <VListItemTitle>{{ proxy.running ? '停止' : '启动' }}</VListItemTitle>
                </VListItem>
                <VListItem @click="deleteProxy(proxy)" class="text-error">
                  <template #prepend>
                    <VIcon icon="ri-delete-bin-line" />
                  </template>
                  <VListItemTitle>删除</VListItemTitle>
                </VListItem>
              </VList>
            </VMenu>
          </VCardTitle>

          <VCardText>
            <div class="proxy-info mb-4">
              <div class="d-flex align-center mb-2">
                <VIcon icon="ri-global-line" size="16" class="me-2" />
                <span class="text-body-2">端口: {{ proxy.listen_port }}</span>
              </div>
              <div v-if="proxy.listen_host" class="d-flex align-center mb-2">
                <VIcon icon="ri-computer-line" size="16" class="me-2" />
                <span class="text-body-2">主机: {{ proxy.listen_host }}</span>
              </div>
              <div v-if="proxy.ssl_certificate" class="d-flex align-center mb-2">
                <VIcon icon="ri-shield-check-line" size="16" class="me-2" />
                <span class="text-body-2">SSL 已启用</span>
              </div>
            </div>

            <!-- Locations -->
            <div>
              <div class="d-flex align-center justify-space-between mb-3">
                <span class="text-subtitle-2">Location 配置</span>
                <VBtn
                  size="small"
                  variant="text"
                  icon="ri-add-line"
                  @click="openLocationDialog(proxy)"
                />
              </div>

              <!-- 没有 locations 时的提示 -->
              <div v-if="!proxy.locations || proxy.locations.length === 0" class="text-center py-4">
                <VIcon icon="ri-information-line" size="24" class="mb-2 text-warning" />
                <div class="text-body-2 text-medium-emphasis">
                  该代理暂无 Location 配置，无法启动
                </div>
                <div class="text-caption text-disabled">
                  请添加至少一个 Location 配置
                </div>
              </div>

              <!-- 有 locations 时显示列表 -->
              <div v-else>

              <VList density="compact" class="location-list">
                <VListItem
                  v-for="(location, index) in proxy.locations"
                  :key="index"
                  class="location-item"
                  :class="{ 'location-disabled': !location.enabled }"
                >
                  <template #prepend>
                    <div class="d-flex align-center">
                      <VIcon
                        :icon="location.proxy_pass ? 'ri-external-link-line' : 'ri-folder-line'"
                        size="16"
                        :class="{ 'text-disabled': !location.enabled }"
                      />
                      <VChip
                        :color="location.enabled ? 'success' : 'error'"
                        size="x-small"
                        class="ms-2"
                      >
                        {{ location.enabled ? '启用' : '禁用' }}
                      </VChip>
                    </div>
                  </template>

                  <VListItemTitle class="text-body-2" :class="{ 'text-disabled': !location.enabled }">
                    {{ location.path }}
                  </VListItemTitle>

                  <VListItemSubtitle class="text-caption" :class="{ 'text-disabled': !location.enabled }">
                    {{ location.name || (location.proxy_pass || location.root) }}
                  </VListItemSubtitle>

                  <template #append>
                    <div class="d-flex align-center">
                      <!-- Enable/Disable toggle -->
                      <VBtn
                        :icon="location.enabled ? 'ri-eye-line' : 'ri-eye-off-line'"
                        :color="location.enabled ? 'success' : 'error'"
                        size="x-small"
                        variant="text"
                        @click="toggleLocationEnabled(proxy, location)"
                        :title="location.enabled ? '点击禁用' : '点击启用'"
                      />

                      <!-- Move buttons -->
                      <VBtn
                        v-if="index > 0"
                        icon="ri-arrow-up-s-line"
                        size="x-small"
                        variant="text"
                        @click="moveLocation(proxy, index, index - 1)"
                      />
                      <VBtn
                        v-if="index < proxy.locations.length - 1"
                        icon="ri-arrow-down-s-line"
                        size="x-small"
                        variant="text"
                        @click="moveLocation(proxy, index, index + 1)"
                      />

                      <!-- Action buttons -->
                      <VBtn
                        icon="ri-pencil-line"
                        size="x-small"
                        variant="text"
                        @click="openLocationDialog(proxy, index)"
                      />
                      <VBtn
                        icon="ri-delete-bin-line"
                        size="x-small"
                        variant="text"
                        color="error"
                        @click="deleteLocation(proxy, location)"
                      />
                    </div>
                  </template>
                </VListItem>
              </VList>
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

        <!-- Empty State -->
        <div v-if="!loading && filteredProxies.length === 0" class="text-center py-12">
          <VIcon icon="ri-server-line" size="64" class="text-disabled mb-4" />
          <div class="text-h6 mb-2">{{ search ? '未找到匹配的代理' : '暂无代理配置' }}</div>
          <div class="text-body-2 text-disabled mb-4">
            {{ search ? '尝试调整搜索条件' : '创建您的第一个代理配置' }}
          </div>
          <VBtn v-if="!search" color="primary" prepend-icon="ri-add-line" @click="openAddDialog">
            新建代理
          </VBtn>
        </div>

        <!-- Loading -->
        <div v-if="loading" class="text-center py-12">
          <VProgressCircular indeterminate color="primary" size="48" />
          <div class="text-body-2 mt-4">加载中...</div>
        </div>
      </VCardText>
    </VCard>
  </VContainer>

  <!-- Add/Edit Proxy Dialog -->
  <VDialog v-model="dialog" max-width="800px" persistent scrollable>
    <VCard>
      <VCardTitle class="d-flex align-center">
        <VIcon :icon="isEditMode ? 'ri-pencil-line' : 'ri-add-line'" class="me-3" />
        <span>{{ isEditMode ? '编辑代理' : '新建代理' }}</span>
      </VCardTitle>

      <VDivider />

      <VCardText class="pa-6">
        <VForm>
          <VRow>
            <!-- Basic Settings -->
            <VCol cols="12">
              <div class="text-h6 mb-4">基本设置</div>
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="editedProxy.id"
                label="代理 ID"
                :disabled="isEditMode"
                hint="留空将自动生成"
                persistent-hint
                variant="outlined"
              />
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="editedProxy.name"
                label="代理名称"
                hint="便于识别的别名"
                persistent-hint
                variant="outlined"
              />
            </VCol>

            <VCol cols="12" md="6">
              <VCheckbox
                v-model="editedProxy.enabled"
                label="启用此代理"
                hint="禁用后代理将不会启动"
                persistent-hint
                color="success"
              />
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model.number="editedProxy.listen_port"
                label="监听端口 *"
                type="number"
                :rules="[rules.required, rules.port]"
                variant="outlined"
              />
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="editedProxy.listen_host"
                label="监听地址"
                hint="留空表示监听所有地址"
                persistent-hint
                variant="outlined"
              />
            </VCol>

            <!-- SSL Settings -->
            <VCol cols="12">
              <div class="text-h6 mb-4 mt-4">SSL 设置</div>
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="editedProxy.ssl_certificate"
                label="SSL 证书文件路径"
                hint="例如: /path/to/cert.pem"
                persistent-hint
                variant="outlined"
              />
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="editedProxy.ssl_certificate_key"
                label="SSL 私钥文件路径"
                hint="例如: /path/to/key.pem"
                persistent-hint
                variant="outlined"
              />
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions class="pa-4">
        <VSpacer />
        <VBtn variant="text" @click="closeDialog">取消</VBtn>
        <VBtn
          color="primary"
          :loading="loading"
          @click="saveProxy"
        >
          {{ isEditMode ? '更新' : '创建' }}
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>

  <!-- Add/Edit Location Dialog -->
  <VDialog v-model="locationDialog" max-width="900px" persistent scrollable>
    <VCard>
      <VCardTitle class="d-flex align-center">
        <VIcon :icon="isLocationEditMode ? 'ri-pencil-line' : 'ri-add-line'" class="me-3" />
        <span>{{ isLocationEditMode ? '编辑 Location' : '新建 Location' }}</span>
      </VCardTitle>

      <VDivider />

      <VCardText class="pa-6">
        <VForm>
          <VRow>
            <!-- Basic Settings -->
            <VCol cols="12">
              <div class="text-h6 mb-4">基本设置</div>
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="editedLocation.name"
                label="Location 名称"
                hint="便于识别的别名"
                persistent-hint
                variant="outlined"
              />
            </VCol>

            <VCol cols="12" md="6">
              <VTextField
                v-model="editedLocation.path"
                label="路径 *"
                :rules="[rules.required, rules.path]"
                hint="例如: / 或 /api"
                persistent-hint
                variant="outlined"
              />
            </VCol>

            <!-- Target Settings -->
            <VCol cols="12">
              <div class="text-h6 mb-4 mt-4">目标设置</div>
              <div class="text-body-2 text-disabled mb-4">
                配置反向代理目标或静态文件根目录（二选一）
              </div>
            </VCol>

            <VCol cols="12">
              <VTextField
                v-model="editedLocation.proxy_pass"
                label="反向代理目标"
                :rules="[rules.url]"
                hint="例如: http://localhost:3000"
                persistent-hint
                variant="outlined"
              />
            </VCol>

            <VCol cols="12">
              <VTextField
                v-model="editedLocation.root"
                label="静态文件根目录"
                hint="例如: /var/www/html"
                persistent-hint
                variant="outlined"
              />
            </VCol>

            <!-- Advanced Settings -->
            <VCol cols="12">
              <div class="text-h6 mb-4 mt-4">高级设置</div>
            </VCol>

            <VCol cols="12" md="4">
              <VCheckbox
                v-model="editedLocation.enabled"
                label="启用此 Location"
                hint="禁用后此规则将不会生效"
                persistent-hint
                color="success"
              />
            </VCol>

            <VCol cols="12" md="4">
              <VSelect
                v-model="editedLocation.allow_deny"
                label="访问控制"
                :items="[
                  { title: '无限制', value: '' },
                  { title: '允许', value: 'allow' },
                  { title: '拒绝', value: 'deny' }
                ]"
                variant="outlined"
              />
            </VCol>

            <VCol cols="12" md="4">
              <VCheckbox
                v-model="editedLocation.insecure_skip_verify"
                label="跳过 SSL 证书验证"
                hint="仅用于开发环境"
                persistent-hint
              />
            </VCol>

            <!-- Custom Headers -->
            <VCol cols="12">
              <div class="d-flex align-center justify-space-between mb-4">
                <div class="text-h6">自定义请求头</div>
                <VBtn
                  size="small"
                  variant="outlined"
                  prepend-icon="ri-add-line"
                  @click="addHeaderField"
                >
                  添加请求头
                </VBtn>
              </div>

              <div v-if="editedLocation.proxy_set_header && Object.keys(editedLocation.proxy_set_header).length > 0">
                <VRow
                  v-for="(value, key, index) in editedLocation.proxy_set_header"
                  :key="index"
                  class="mb-2"
                >
                  <VCol cols="5">
                    <VTextField
                      :model-value="key"
                      label="请求头名称"
                      variant="outlined"
                      density="compact"
                      @update:model-value="(newKey) => {
                        if (newKey !== key) {
                          editedLocation.proxy_set_header[newKey] = value
                          delete editedLocation.proxy_set_header[key]
                        }
                      }"
                    />
                  </VCol>
                  <VCol cols="5">
                    <VTextField
                      v-model="editedLocation.proxy_set_header[key]"
                      label="请求头值"
                      variant="outlined"
                      density="compact"
                    />
                  </VCol>
                  <VCol cols="2" class="d-flex align-center">
                    <VBtn
                      icon="ri-delete-bin-line"
                      size="small"
                      variant="text"
                      color="error"
                      @click="removeHeaderField(key)"
                    />
                  </VCol>
                </VRow>
              </div>

              <div v-else class="text-center py-4">
                <VIcon icon="ri-file-list-3-line" size="32" class="text-disabled mb-2" />
                <div class="text-body-2 text-disabled">暂无自定义请求头</div>
              </div>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>

      <VDivider />

      <VCardActions class="pa-4">
        <VSpacer />
        <VBtn variant="text" @click="closeLocationDialog">取消</VBtn>
        <VBtn
          color="primary"
          :loading="loading"
          @click="saveLocation"
        >
          {{ isLocationEditMode ? '更新' : '创建' }}
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>

  <!-- Snackbar for notifications -->
  <VSnackbar v-model="snackbar" :color="snackbarColor" :timeout="3000">
    {{ snackbarText }}
  </VSnackbar>
</template>

<style scoped>
.proxy-management {
  padding: 0;
}

.proxy-card {
  height: 100%;
  transition: all 0.3s ease;
}

.proxy-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.proxy-info {
  background: rgba(var(--v-theme-surface-variant), 0.1);
  border-radius: 8px;
  padding: 12px;
}

.location-list {
  background: rgba(var(--v-theme-surface-variant), 0.05);
  border-radius: 8px;
  border: 1px solid rgba(var(--v-theme-outline), 0.12);
}

.location-item {
  border-bottom: 1px solid rgba(var(--v-theme-outline), 0.08);
  transition: all 0.2s ease;
}

.location-item:last-child {
  border-bottom: none;
}

.location-item:hover {
  background: rgba(var(--v-theme-primary), 0.04);
}

.location-item.location-disabled {
  opacity: 0.6;
  background: rgba(var(--v-theme-error), 0.02);
}

.location-item.location-disabled:hover {
  background: rgba(var(--v-theme-error), 0.06);
}
</style>
