import { defineStore } from 'pinia'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    // Initialize auth from localStorage to persist login state across page reloads
    auth: JSON.parse(localStorage.getItem('auth') || 'false'),
  }),
  actions: {
    login() {
      this.auth = true
      localStorage.setItem('auth', 'true')
    },
    logout() {
      this.auth = false
      localStorage.removeItem('auth')
    },
  },
})
