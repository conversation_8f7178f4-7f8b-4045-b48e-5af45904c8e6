@use "@core/scss/base/mixins";
@use "@configured-variables" as variables;
@use "vuetify/lib/styles/tools/states" as vuetifyStates;

%nav-header-action {
  font-size: 1.25rem;
}

// Nav items styles (including section title)
%vertical-nav-item {
  margin-block: 0;
  margin-inline: variables.$vertical-nav-horizontal-spacing;
  padding-block: 0;
  padding-inline: variables.$vertical-nav-horizontal-padding;
  white-space: nowrap;
}

// This is same as `%vertical-nav-item` except section title is excluded
%vertical-nav-item-interactive {
  border-radius: 0.4rem;
  block-size: 2.75rem;

  /*
    ℹ️ We will use `margin-block-end` instead of `margin-block` to give more space for shadow to appear.
    With `margin-block`, due to small space (space gets divided between top & bottom) shadow cuts
  */
  margin-block-end: 0.375rem;
}

// Common styles for nav item icon styles
// ℹ️ Nav group's children icon styles are not here (Adjusts height, width & margin)
%vertical-nav-items-icon {
  flex-shrink: 0;
  font-size: variables.$vertical-nav-items-icon-size;
  margin-inline-end: variables.$vertical-nav-items-icon-margin-inline-end;
}

// ℹ️ Icon styling for icon nested inside another nav item (2nd level)
%vertical-nav-items-nested-icon {
  /*
    ℹ️ `margin-inline` will be (normal icon font-size - small icon font-size) / 2
    (1.5rem - 0.9rem) / 2 => 0.6rem / 2 => 0.3rem
  */
  $vertical-nav-items-nested-icon-margin-inline: calc((variables.$vertical-nav-items-icon-size - variables.$vertical-nav-items-nested-icon-size) / 2);

  font-size: variables.$vertical-nav-items-nested-icon-size;
  margin-inline: $vertical-nav-items-nested-icon-margin-inline $vertical-nav-items-nested-icon-margin-inline + variables.$vertical-nav-items-icon-margin-inline-end;
}

%vertical-nav-items-icon-after-2nd-level {
  visibility: hidden;
}

// Open & Active nav group styles
%vertical-nav-group-open-active {
  @include mixins.selected-states("&::before");
}

// Section title
// ℹ️ Setting height will prevent jerking when text & icon is toggled
%vertical-nav-section-title {
  block-size: 1.5rem;
  color: rgba(var(--v-theme-on-surface), var(--v-disabled-opacity));
  font-size: 0.75rem;
  text-transform: uppercase;
}

// Vertical nav item badge styles
%vertical-nav-item-badge {
  display: inline-block;
  border-radius: 1.5rem;
  font-size: 0.8em;
  font-weight: 500;
  line-height: 1;
  padding-block: 0.25em;
  padding-inline: 0.55em;
  text-align: center;
  vertical-align: baseline;
  white-space: nowrap;
}
