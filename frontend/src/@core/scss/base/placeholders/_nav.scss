@use "@core/scss/base/mixins";

// ℹ️ This is common style that needs to be applied to both navs
%nav {
  color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));

  .nav-item-title {
    letter-spacing: 0.15px;
  }

  .nav-section-title {
    letter-spacing: 0.4px;
  }
}

/*
    Active nav link styles for horizontal & vertical nav

    For horizontal nav it will be only applied to top level nav items
    For vertical nav it will be only applied to nav links (not nav groups)
*/
%nav-link-active {
  background-color: rgb(var(--v-theme-primary));
  color: rgb(var(--v-theme-on-primary));

  @include mixins.elevation(3);
}

%nav-link {
  a {
    color: inherit;
  }
}
