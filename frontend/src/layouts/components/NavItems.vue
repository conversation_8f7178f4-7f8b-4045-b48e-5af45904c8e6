<script setup>
import VerticalNavSectionTitle from '@/@layouts/components/VerticalNavSectionTitle.vue'
import VerticalNavGroup from '@layouts/components/VerticalNavGroup.vue'
import VerticalNavLink from '@layouts/components/VerticalNavLink.vue'
</script>

<template>
  <VerticalNavSectionTitle
      :item="{
      heading: '功能特性',
    }"
  />
  <VerticalNavLink
      :item="{
      title: 'Web代理',
      icon: 'ri-server-line',
      to: '/proxy',
    }"
  />

  <VerticalNavSectionTitle
      :item="{
      heading: '飞牛',
    }"
  />
  <VerticalNavLink
      :item="{
      title: '应用管理',
      icon: 'ri-information-line',
      to: '/web-proxy',
    }"
  />

  <VerticalNavSectionTitle
      :item="{
      heading: '内部功能',
    }"
  />
  <VerticalNavLink
      :item="{
    title: '系统设置',
    icon: 'ri-user-settings-line',
    to: '/settings',
  }"
  />
</template>
