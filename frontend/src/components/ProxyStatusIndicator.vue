<template>
  <div class="status-indicator d-flex align-center">
    <VIcon 
      :icon="statusIcon" 
      :color="statusColor" 
      size="16" 
      class="me-2"
    />
    <span class="text-body-2" :class="`text-${statusColor}`">
      {{ statusText }}
    </span>
    <VProgressCircular 
      v-if="loading" 
      indeterminate 
      size="16" 
      width="2" 
      class="ms-2"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  isRunning: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const statusIcon = computed(() => {
  if (props.loading) return 'ri-loader-4-line'
  return props.isRunning ? 'ri-play-circle-fill' : 'ri-stop-circle-fill'
})

const statusColor = computed(() => {
  if (props.loading) return 'warning'
  return props.isRunning ? 'success' : 'error'
})

const statusText = computed(() => {
  if (props.loading) return '处理中...'
  return props.isRunning ? '运行中' : '已停止'
})
</script>

<style scoped>
.status-indicator {
  min-width: 80px;
}
</style>
